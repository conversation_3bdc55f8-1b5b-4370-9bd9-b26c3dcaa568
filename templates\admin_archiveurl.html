{% extends "header_admin.html" %}

{% block content %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Archive URL</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div, table, tr, th, td {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark theme base styles */
        body {
            background: linear-gradient(135deg, #141719 0%, #121212 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        /* Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
            min-height: calc(100vh - 200px);
        }

        /* Page title */
        .page-title {
            font-family: 'Roboto', sans-serif;
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 38px;
            text-align: center;
            letter-spacing: -0.04em;
        }

        /* Main content container */
        .content {
            width: 100%;
            background-color: transparent;
            margin: 0 auto;
            border-radius: 12px;
            padding: 20px 0;
            box-sizing: border-box;
        }

        /* Button styles */
        .btn {
            display: inline-block;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            border-radius: 30px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .btn-primary {
            background-color: #b148f3;
            color: #ffffff;
        }

        .btn-primary:hover {
            background-color: #9a3de0;
            transform: scale(1.05);
        }

        /* Flash messages container */
        .flash-messages {
            position: fixed;
            top: 85px;
            right: -2px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .flash-messages li {
            background-color: #ff0019a8;
            color: white;
            padding: 15px 25px;
            margin-top: 10px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            font-size: 14px;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 5s forwards;
            cursor: pointer;
            min-width: 250px;
        }

        /* Animation for slide in from right and fade out */
        @keyframes slideInOut {
            0% {
                opacity: 0;
                transform: translateX(100%);
            }
            10% {
                opacity: 1;
                transform: translateX(0);
            }
            90% {
                opacity: 1;
                transform: translateX(0);
            }
            100% {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        /* Table Styling */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 14px;
        }

        th {
            background-color: #282828;
            color: #ffffff;
            font-weight: 600;
            text-align: left;
            padding: 15px;
            border-bottom: 2px solid #333333;
        }

        td {
            padding: 15px;
            border-bottom: 1px solid #333333;
            color: #b3b3b3;
            vertical-align: middle;
        }

        tr:hover {
            background-color: #222222;
        }

        tr:last-child td {
            border-bottom: none;
        }

        /* URL column with truncation */
        .url-cell {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        /* Card Styles */
        .card {
            background-color: #282828;
            border: 1px solid #333333;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: #333333;
            padding: 15px;
            font-size: 1.25rem;
            font-weight: bold;
            border-bottom: 1px solid #444444;
            border-radius: 8px 8px 0 0;
            color: #ffffff;
        }

        .card-body {
            padding: 20px;
            color: #b3b3b3;
        }

        /* Alert styles for flash messages */
        .alert {
            padding: 12px 20px;
            margin-bottom: 15px;
            border-radius: 8px;
            color: #ffffff;
        }

        .alert-danger {
            background-color: #dc3545;
            border: 1px solid #c82333;
        }

        .alert-success {
            background-color: #28a745;
            border: 1px solid #1e7e34;
        }

        .alert-info {
            background-color: #17a2b8;
            border: 1px solid #138496;
        }
        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
                margin: 20px;
            }

            .page-title {
                font-size: 36px;
                margin-bottom: 30px;
            }

            table, thead, tbody, th, td, tr {
                display: block;
            }

            thead tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
            }

            tr {
                margin-bottom: 15px;
                background-color: #222222;
                border-radius: 8px;
                overflow: hidden;
            }

            td {
                position: relative;
                padding-left: 50%;
                text-align: center;
                border-bottom: 1px solid #333333;
            }

            td:before {
                position: absolute;
                top: 15px;
                left: 15px;
                width: 45%;
                padding-right: 10px;
                white-space: nowrap;
                text-align: center;
                font-weight: bold;
                content: attr(data-label);
                color: #ffffff;
            }

            td:nth-of-type(1):before { content: "No."; }
            td:nth-of-type(2):before { content: "URL"; }
            td:nth-of-type(3):before { content: "Classification"; }
            td:nth-of-type(4):before { content: "Note"; }
            td:nth-of-type(5):before { content: "Sender"; }
            td:nth-of-type(6):before { content: "View Detail"; }
            td:nth-of-type(7):before { content: "Edit"; }
            td:nth-of-type(8):before { content: "Remove"; }

            .url-cell {
                max-width: none;
                white-space: normal;
                word-break: break-all;
            }
        }

        @media (max-width: 480px) {
            .page-title {
                font-size: 28px;
            }

            td {
                padding: 12px 15px 12px 50%;
                font-size: 13px;
            }

            .btn {
                padding: 10px 20px;
                font-size: 14px;
            }
        }

        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 40px 0;
            color: #b3b3b3;
            font-style: italic;
        }

        /* Modal styles for confirmation popup */
        .modal-overlay {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: #282828;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            text-align: center;
            max-width: 500px;
            width: 90%;
            border: 1px solid #333333;
        }

        .modal-content h2 {
            color: #ffffff;
            margin-bottom: 15px;
            font-size: 24px;
            font-weight: 600;
        }

        .modal-content p {
            color: #b3b3b3;
            margin-bottom: 25px;
            font-size: 16px;
            line-height: 1.5;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .modal-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .btn-confirm {
            background-color: #b148f3;
            color: #ffffff;
        }

        .btn-confirm:hover {
            background-color: #9a3de0;
            transform: scale(1.05);
        }

        .btn-cancel {
            background-color: #333333;
            color: #ffffff;
        }

        .btn-cancel:hover {
            background-color: #444444;
            transform: scale(1.05);
        }

        /* Delete button styles */
        .delete-btn {
            background-color: #dc3545;
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .delete-btn:hover {
            background-color: #c82333;
            transform: scale(1.1);
        }

        .delete-icon {
            font-size: 16px;
        }

        /* Edit button styles */
        .edit-btn {
            background-color: #ffc107;
            color: #000000;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
        }

        .edit-btn:hover {
            background-color: #e0a800;
            transform: scale(1.1);
        }

        .edit-icon {
            font-size: 16px;
        }

        /* Detail button styles */
        .detail-button {
            display: inline-block;
            padding: 8px 15px;
            background-color: #333333;
            color: #ffffff;
            border: none;
            border-radius: 30px;
            font-size: 12px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            text-align: center;
            line-height: 1.4;
        }

        .detail-button:hover {
            background-color: #b148f3;
            color: #ffffff;
            transform: scale(1.05);
        }

        /* Top controls container */
        .top-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            gap: 20px;
        }

        .csv-export-section {
            flex: 0 0 auto;
        }

        .filter-section {
            flex: 0 0 auto;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Minimalist search input */
        .search-input {
            width: 200px;
            padding: 8px 12px;
            border: 1px solid #333333;
            border-radius: 6px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #4aa4c0;
            box-shadow: 0 0 0 1px rgba(74, 164, 192, 0.3);
        }

        .search-input::placeholder {
            color: #666666;
            font-style: italic;
        }

        .search-clear-btn {
            background: none;
            border: none;
            color: #666666;
            cursor: pointer;
            font-size: 14px;
            padding: 4px 6px;
            border-radius: 3px;
            transition: all 0.3s ease;
            display: none;
            margin-left: -25px;
            position: relative;
            z-index: 1;
        }

        .search-clear-btn:hover {
            color: #4aa4c0;
            background-color: rgba(74, 164, 192, 0.1);
        }

        .search-clear-btn.show {
            display: block;
        }

        /* Classification filter dropdown */
        .classification-filter {
            padding: 8px 12px;
            border: 1px solid #333333;
            border-radius: 6px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 13px;
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 120px;
        }

        .classification-filter:focus {
            outline: none;
            border-color: #4aa4c0;
            box-shadow: 0 0 0 1px rgba(74, 164, 192, 0.3);
        }

        .classification-filter option {
            background-color: #1a1a1a;
            color: #ffffff;
            padding: 8px;
        }

        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 40px 0;
            color: #b3b3b3;
            font-style: italic;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .top-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .filter-section {
                flex-direction: column;
                gap: 10px;
            }

            .search-section {
                justify-content: flex-end;
            }

            .search-input {
                width: 180px;
            }

            .classification-filter {
                min-width: 100px;
            }
        }

        @media (max-width: 480px) {
            .top-controls {
                gap: 8px;
            }

            .filter-section {
                gap: 8px;
            }

            .search-input {
                width: 160px;
                font-size: 12px;
                padding: 6px 10px;
            }

            .classification-filter {
                font-size: 12px;
                padding: 6px 10px;
                min-width: 90px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">Archive of Scanned URLs</h1>
        <div class="content">

            <!-- Top Controls: CSV Export, Classification Filter and Search -->
            <div class="top-controls">
                <div class="csv-export-section">
                    <form id="csvForm" action="{{ url_for('export_csv') }}?from=admin" method="get">
                        <button type="button" class="btn btn-primary" onclick="showConfirmationModal()">Print to CSV file</button>
                    </form>
                </div>

                <div class="filter-section">
                    <select id="classification_filter" class="classification-filter">
                        <option value="">All Classifications</option>
                        <option value="Safe" {{ 'selected' if classification_filter == 'Safe' else '' }}>Safe</option>
                        <option value="Suspicious" {{ 'selected' if classification_filter == 'Suspicious' else '' }}>Suspicious</option>
                    </select>

                    <div class="search-section">
                        <input type="text"
                               id="search_sender"
                               class="search-input"
                               value="{{ search_sender or '' }}"
                               placeholder="Search by sender..."
                               autocomplete="off">
                        <button type="button" id="clearSearch" class="search-clear-btn" title="Clear search">✕</button>
                    </div>
                </div>
            </div>

            <!-- Search Results Info -->
            

            <table>
                <thead>
                    <tr>
                        <th>No.</th>
                        <th>URL</th>
                        <th style="text-align: center;">Classification</th>
                        <th>Note</th>
                        <th style="text-align: center;">Sender</th>
                        <th style="text-align: center;">View Detail</th>
                        <th style="text-align: center;">Edit</th>
                        <th style="text-align: center;">Remove</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in results %}
                    <tr>
                        <td data-label="No.">{{ loop.index }}</td>
                        <td data-label="URL" class="url-cell">{{ row.url }}</td>
                        <td data-label="Classification" style="text-align: center;">
                            <span style="display: inline-block; padding: 5px 10px; border-radius: 5px; font-size: 12px; font-weight: 600; text-align: center; min-width: 80px; background-color: {% if row.classification == 'Safe' %}#1DB954; color: #000000;{% else %}#E91429; color: #ffffff;{% endif %}">
                                {{ row.classification }}
                            </span>
                        </td>
                        <td data-label="Note">{{ row.note }}</td>
                        <td data-label="Sender" style="text-align: center;">{{ row.sender }}</td>
                        <td data-label="View Detail" style="text-align: center;">
                            <a href="{{ url_for('admin_details_by_url', url_encoded=row.url|urlencode) }}" class="detail-button">View Details</a>
                        </td>
                        <td data-label="Edit" style="text-align: center;">
                            <button class="edit-btn" onclick="showEditModal({{ row.id }}, '{{ row.classification }}', '{{ row.note|replace("'", "\\'") }}')">
                                <i class="edit-icon">✏️</i>
                            </button>
                        </td>
                        <td data-label="Remove" style="text-align: center;">
                            <button class="delete-btn" onclick="showDeleteModal({{ row.id }}, '{{ row.url|truncate(30) }}')">
                                <i class="delete-icon">🗑️</i>
                            </button>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="8" class="empty-state">
                            {% if search_sender or classification_filter %}
                                No URLs found matching your filters
                                {% if search_sender %} for sender containing "{{ search_sender }}"{% endif %}
                                {% if classification_filter %} with classification "{{ classification_filter }}"{% endif %}.
                                Try different filters or <a href="{{ url_for('admin_archiveurl') }}" style="color: #b148f3; text-decoration: underline;">clear all filters</a> to view all URLs.
                            {% else %}
                                No archive data found. URLs will appear here after being scanned.
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    <ul class="flash-messages" id="flash-messages">
        {% for category, message in messages %}
        <li>{{ message }}</li>
        {% endfor %}
    </ul>
    {% endif %}
    {% endwith %}

    <!-- Confirmation Modal -->
    <div class="modal-overlay" id="confirmationModal">
        <div class="modal-content">
            <h2>CSV Export Instructions</h2>
            <p>When you click "Export CSV", the file will be downloaded to your browser's default download location.</p>
            <p style="margin-top: 15px; color: #ffffff; font-weight: 600;">Please choose where you want to save the CSV file:</p>
            <ul style="text-align: left; margin: 15px 0; color: #b3b3b3; padding-left: 20px;">
                <li>Your browser will prompt you to choose a save location</li>
                <li>Select your preferred folder (e.g., Desktop, Documents, etc.)</li>
                <li>The file will be named automatically as "urlcheck_dataset_DDMMYYYY_HHMMSS.csv"</li>
                <li>Example: "urlcheck_dataset_15012025_143022.csv"</li>
            </ul>
            <div class="modal-buttons">
                <button class="modal-btn btn-confirm" onclick="confirmExport()">Export CSV</button>
                <button class="modal-btn btn-cancel" onclick="closeModal()">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal-overlay" id="deleteModal">
        <div class="modal-content">
            <h2>Delete URL Confirmation</h2>
            <p>Are you sure you want to delete this URL from the archive?</p>
            <p style="margin-top: 15px; color: #ffffff; font-weight: 600;">URL: <span id="urlToDelete" style="color: #b3b3b3;"></span></p>
            <p style="margin-top: 15px; color: #ff6b6b; font-weight: 600;">⚠️ This action cannot be undone!</p>

            <form id="deleteForm" action="{{ url_for('delete_scan_result') }}" method="post" style="margin-top: 20px;">
                <input type="hidden" id="scanIdToDelete" name="scan_id" value="">

                <div style="margin-bottom: 15px;">
                    <label for="adminPassword" style="display: block; color: #ffffff; margin-bottom: 8px; font-weight: 600;">Enter Admin Password:</label>
                    <input type="password" id="adminPassword" name="admin_password" required
                           style="width: 100%; padding: 10px; border: 1px solid #333333; border-radius: 6px; background-color: #1a1a1a; color: #ffffff; font-size: 14px;">
                </div>

                <div class="modal-buttons">
                    <button type="submit" class="modal-btn btn-confirm" style="background-color: #dc3545;">Delete URL</button>
                    <button type="button" class="modal-btn btn-cancel" onclick="closeDeleteModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal-overlay" id="editModal">
        <div class="modal-content">
            <h2>Edit URL Information</h2>
            <form id="editForm">
                <input type="hidden" id="editScanId" name="scan_id">

                <div style="margin-bottom: 20px; text-align: left;">
                    <label for="editClassification" style="display: block; margin-bottom: 8px; color: #ffffff; font-weight: 600;">Classification:</label>
                    <select id="editClassification" name="classification" style="width: 100%; padding: 10px; border: 1px solid #333333; border-radius: 6px; background-color: #1a1a1a; color: #ffffff; font-size: 14px;">
                        <option value="Safe">Safe</option>
                        <option value="Suspicious">Suspicious</option>
                    </select>
                </div>

                <div style="margin-bottom: 25px; text-align: left;">
                    <label for="editNote" style="display: block; margin-bottom: 8px; color: #ffffff; font-weight: 600;">Note:</label>
                    <textarea id="editNote" name="note" rows="4" style="width: 100%; padding: 10px; border: 1px solid #333333; border-radius: 6px; background-color: #1a1a1a; color: #ffffff; font-size: 14px; resize: vertical;" placeholder="Enter note..."></textarea>
                </div>

                <div class="modal-buttons">
                    <button type="button" class="modal-btn btn-confirm" onclick="showPasswordConfirmation()">Save Changes</button>
                    <button type="button" class="modal-btn btn-cancel" onclick="closeEditModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Password Confirmation Modal -->
    <div class="modal-overlay" id="passwordModal">
        <div class="modal-content">
            <h2>Admin Password Confirmation</h2>
            <p>Please enter your admin password to confirm the changes:</p>
            <form id="passwordForm" action="{{ url_for('update_scan_result') }}" method="post">
                <input type="hidden" id="confirmScanId" name="scan_id">
                <input type="hidden" id="confirmClassification" name="classification">
                <input type="hidden" id="confirmNote" name="note">

                <div style="margin-bottom: 25px; text-align: left;">
                    <label for="adminPasswordEdit" style="display: block; margin-bottom: 8px; color: #ffffff; font-weight: 600;">Admin Password:</label>
                    <input type="password" id="adminPasswordEdit" name="admin_password" required style="width: 100%; padding: 10px; border: 1px solid #333333; border-radius: 6px; background-color: #1a1a1a; color: #ffffff; font-size: 14px;" placeholder="Enter admin password">
                </div>

                <div class="modal-buttons">
                    <button type="submit" class="modal-btn btn-confirm" style="background-color: #ffc107; color: #000000;">Confirm Update</button>
                    <button type="button" class="modal-btn btn-cancel" onclick="closePasswordModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>


    <script>
        // Real-time search functionality
        let searchTimeout;
        const searchInput = document.getElementById('search_sender');
        const clearButton = document.getElementById('clearSearch');
        const classificationFilter = document.getElementById('classification_filter');

        // Show/hide clear button based on input content
        function toggleClearButton() {
            if (searchInput.value.trim()) {
                clearButton.classList.add('show');
            } else {
                clearButton.classList.remove('show');
            }
        }

        // Perform search with debouncing
        function performSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const searchTerm = searchInput.value.trim();
                const classificationValue = classificationFilter.value;
                const currentUrl = new URL(window.location);

                if (searchTerm) {
                    currentUrl.searchParams.set('search_sender', searchTerm);
                } else {
                    currentUrl.searchParams.delete('search_sender');
                }

                if (classificationValue) {
                    currentUrl.searchParams.set('classification_filter', classificationValue);
                } else {
                    currentUrl.searchParams.delete('classification_filter');
                }

                // Navigate to the new URL
                window.location.href = currentUrl.toString();
            }, 500); // 500ms delay for debouncing
        }

        // Clear search
        function clearSearch() {
            searchInput.value = '';
            classificationFilter.value = '';
            toggleClearButton();
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('search_sender');
            currentUrl.searchParams.delete('classification_filter');
            window.location.href = currentUrl.toString();
        }

        // Event listeners
        searchInput.addEventListener('input', function() {
            toggleClearButton();
            performSearch();
        });

        classificationFilter.addEventListener('change', function() {
            performSearch();
        });

        clearButton.addEventListener('click', clearSearch);

        // Initialize clear button visibility on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleClearButton();
        });

        // Show confirmation modal
        function showConfirmationModal() {
            document.getElementById('confirmationModal').style.display = 'flex';
        }

        // Close modal
        function closeModal() {
            document.getElementById('confirmationModal').style.display = 'none';
        }

        // Confirm export and submit form
        function confirmExport() {
            document.getElementById('csvForm').submit();
        }

        // Show delete confirmation modal
        function showDeleteModal(scanId, urlPreview) {
            document.getElementById('scanIdToDelete').value = scanId;
            document.getElementById('urlToDelete').textContent = urlPreview;
            document.getElementById('adminPassword').value = '';
            document.getElementById('deleteModal').style.display = 'flex';
        }

        // Close delete modal
        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        // Show edit modal
        function showEditModal(scanId, classification, note) {
            document.getElementById('editScanId').value = scanId;
            document.getElementById('editClassification').value = classification;
            document.getElementById('editNote').value = note;
            document.getElementById('editModal').style.display = 'flex';
        }

        // Close edit modal
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // Show password confirmation modal
        function showPasswordConfirmation() {
            const scanId = document.getElementById('editScanId').value;
            const classification = document.getElementById('editClassification').value;
            const note = document.getElementById('editNote').value;

            document.getElementById('confirmScanId').value = scanId;
            document.getElementById('confirmClassification').value = classification;
            document.getElementById('confirmNote').value = note;
            document.getElementById('adminPasswordEdit').value = '';

            document.getElementById('editModal').style.display = 'none';
            document.getElementById('passwordModal').style.display = 'flex';
        }

        // Close password modal
        function closePasswordModal() {
            document.getElementById('passwordModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const confirmModal = document.getElementById('confirmationModal');
            const deleteModal = document.getElementById('deleteModal');
            const editModal = document.getElementById('editModal');
            const passwordModal = document.getElementById('passwordModal');

            if (event.target === confirmModal) {
                closeModal();
            }
            if (event.target === deleteModal) {
                closeDeleteModal();
            }
            if (event.target === editModal) {
                closeEditModal();
            }
            if (event.target === passwordModal) {
                closePasswordModal();
            }
        }

        // Remove flash message on click
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-messages li');
            flashMessages.forEach(function(msg) {
                msg.addEventListener('click', function() {
                    msg.style.animation = 'slideOut 0.5s forwards';
                    setTimeout(() => {
                        msg.remove();
                    }, 500);
                });
            });
        });

        // Add slideOut animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideOut {
                0% {
                    opacity: 1;
                    transform: translateX(0);
                }
                100% {
                    opacity: 0;
                    transform: translateX(100%);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
{% endblock %}