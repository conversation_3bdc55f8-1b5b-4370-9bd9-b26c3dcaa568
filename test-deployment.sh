#!/bin/bash

# URL Scanner Docker Deployment Test Script
# This script tests the complete Docker deployment to ensure everything works correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Test counters
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    print_status "Running: $test_name"
    
    if eval "$test_command"; then
        print_success "$test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        print_error "$test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Test Docker installation
test_docker_installation() {
    command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1
}

# Test Docker Compose installation
test_docker_compose_installation() {
    command -v docker-compose >/dev/null 2>&1
}

# Test if containers are running
test_containers_running() {
    local containers=("urlscanner_web" "urlscanner_mysql" "urlscanner_phpmyadmin")
    for container in "${containers[@]}"; do
        if ! docker ps --filter "name=$container" --filter "status=running" | grep -q "$container"; then
            return 1
        fi
    done
    return 0
}

# Test web application health
test_web_health() {
    curl -f -s http://localhost:5000/health >/dev/null 2>&1
}

# Test main page accessibility
test_main_page() {
    curl -f -s http://localhost:5000/ >/dev/null 2>&1
}

# Test database connectivity
test_database_connectivity() {
    docker-compose exec mysql mysqladmin ping -h localhost --silent 2>/dev/null
}

# Test phpMyAdmin accessibility
test_phpmyadmin() {
    curl -f -s http://localhost:8080/ >/dev/null 2>&1
}

# Test database schema
test_database_schema() {
    local tables=("admin" "users" "token" "contact_us" "scan_results" "url_breakdown")
    for table in "${tables[@]}"; do
        if ! docker-compose exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD:-rootpassword123} -e "DESCRIBE urlscanner.$table;" >/dev/null 2>&1; then
            return 1
        fi
    done
    return 0
}

# Test admin user exists
test_admin_user() {
    docker-compose exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD:-rootpassword123} -e "SELECT COUNT(*) FROM urlscanner.admin WHERE username='admin';" 2>/dev/null | grep -q "1"
}

# Test URL scanning functionality
test_url_scanning() {
    # Test with a simple POST request to the scanning endpoint
    local response=$(curl -s -X POST http://localhost:5000/result -d "url=https://example.com" -H "Content-Type: application/x-www-form-urlencoded")
    echo "$response" | grep -q "example.com"
}

# Test file permissions
test_file_permissions() {
    [ -r "docker-compose.yml" ] && [ -r "Dockerfile" ] && [ -r ".env" ]
}

# Test volume mounts
test_volume_mounts() {
    docker volume ls | grep -q "urlscanner_mysql_data"
}

# Performance test - basic load test
test_basic_load() {
    print_status "Running basic load test (10 concurrent requests)..."
    
    # Simple load test using curl
    for i in {1..10}; do
        curl -s http://localhost:5000/ >/dev/null &
    done
    wait
    
    # Check if service is still responsive
    curl -f -s http://localhost:5000/health >/dev/null 2>&1
}

# Test backup functionality
test_backup_functionality() {
    if [ -f "manage.sh" ]; then
        # Test if backup script exists and is executable
        [ -x "manage.sh" ] && ./manage.sh backup >/dev/null 2>&1
    else
        return 1
    fi
}

# Test log accessibility
test_logs_accessible() {
    docker-compose logs web --tail=10 >/dev/null 2>&1 && \
    docker-compose logs mysql --tail=10 >/dev/null 2>&1
}

# Function to wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -f -s http://localhost:5000/health >/dev/null 2>&1; then
            print_success "Services are ready"
            return 0
        fi
        
        attempt=$((attempt + 1))
        echo -n "."
        sleep 2
    done
    
    print_error "Services failed to become ready within timeout"
    return 1
}

# Main test function
run_all_tests() {
    echo "=========================================="
    echo "    URL Scanner Deployment Tests"
    echo "=========================================="
    echo ""
    
    # Prerequisites tests
    run_test "Docker installation" "test_docker_installation"
    run_test "Docker Compose installation" "test_docker_compose_installation"
    run_test "File permissions" "test_file_permissions"
    
    echo ""
    print_status "Starting services for testing..."
    
    # Start services if not running
    if ! test_containers_running; then
        print_status "Starting Docker services..."
        docker-compose up -d
        wait_for_services
    fi
    
    echo ""
    print_status "Running deployment tests..."
    
    # Container tests
    run_test "Containers running" "test_containers_running"
    run_test "Volume mounts" "test_volume_mounts"
    
    # Service tests
    run_test "Web application health" "test_web_health"
    run_test "Main page accessibility" "test_main_page"
    run_test "Database connectivity" "test_database_connectivity"
    run_test "phpMyAdmin accessibility" "test_phpmyadmin"
    
    # Database tests
    run_test "Database schema" "test_database_schema"
    run_test "Admin user exists" "test_admin_user"
    
    # Functionality tests
    run_test "URL scanning functionality" "test_url_scanning"
    run_test "Log accessibility" "test_logs_accessible"
    
    # Performance tests
    run_test "Basic load test" "test_basic_load"
    
    # Backup tests
    if [ -f "manage.sh" ]; then
        run_test "Backup functionality" "test_backup_functionality"
    fi
    
    echo ""
    echo "=========================================="
    echo "           Test Results Summary"
    echo "=========================================="
    echo "Total Tests: $TESTS_TOTAL"
    echo "Passed: $TESTS_PASSED"
    echo "Failed: $TESTS_FAILED"
    echo ""
    
    if [ $TESTS_FAILED -eq 0 ]; then
        print_success "All tests passed! Deployment is successful."
        echo ""
        print_status "You can now access:"
        echo "  - URL Scanner: http://localhost:5000"
        echo "  - phpMyAdmin: http://localhost:8080"
        echo "  - Admin login: admin / admin123"
        return 0
    else
        print_error "Some tests failed. Please check the issues above."
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "URL Scanner Deployment Test Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  test        Run all deployment tests"
    echo "  quick       Run quick health checks only"
    echo "  load        Run load testing only"
    echo "  help        Show this help message"
}

# Quick test function
run_quick_tests() {
    echo "Running quick health checks..."
    
    run_test "Containers running" "test_containers_running"
    run_test "Web health" "test_web_health"
    run_test "Database connectivity" "test_database_connectivity"
    
    echo ""
    if [ $TESTS_FAILED -eq 0 ]; then
        print_success "Quick tests passed!"
    else
        print_error "Quick tests failed!"
    fi
}

# Load test function
run_load_tests() {
    echo "Running load tests..."
    run_test "Basic load test" "test_basic_load"
}

# Main execution
case "${1:-test}" in
    test)
        run_all_tests
        ;;
    quick)
        run_quick_tests
        ;;
    load)
        run_load_tests
        ;;
    help|*)
        show_usage
        ;;
esac

exit $TESTS_FAILED
