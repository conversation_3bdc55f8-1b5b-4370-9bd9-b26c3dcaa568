{% if is_admin %}
    {% extends "header_admin.html" %}
{% else %}
    {% extends "header.html" %}
{% endif %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URLCHECK - User Guide</title>
    <style>
        /* Reset and base styles */
        body, h1, h2, h3, div, ul, li {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}
        
        /* Main container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
            margin-bottom: 30px;
        }
        
        /* Page title */
        .page-title {
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-top: 47px;
            margin-bottom: 40px;
            text-align: center;
            letter-spacing: -0.04em;
        }
        
        /* Section styling */
        .guide-section {
            background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .section-title {
            font-size: 32px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .section-content {
            font-size: 16px;
            line-height: 1.8;
            color: #e0e0e0;
            margin-bottom: 20px;
        }
        
        /* Feature highlights */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-title {
            font-size: 20px;
            font-weight: 600;
            color: #5dcef0;
            margin-bottom: 15px;
        }
        
        .feature-description {
            font-size: 14px;
            line-height: 1.6;
            color: #c0c0c0;
        }
        
        /* Horizontal step-by-step guide */
        .steps-container {
            margin: 30px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            padding: 20px 0;
        }

        /* Connecting line between steps */
        .steps-container::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #5dcef0, #4a9fd1);
            z-index: 1;
            border-radius: 2px;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            flex: 1;
            max-width: 250px;
            position: relative;
            z-index: 2;
        }

        .step-number {
            background: linear-gradient(135deg, #5dcef0 0%, #4a9fd1 100%);
            color: #000;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 24px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(93, 206, 240, 0.4);
            border: 4px solid #1e1e1e;
            transition: all 0.3s ease;
        }

        .step:hover .step-number {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(93, 206, 240, 0.6);
        }

        .step-content {
            background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
            border-radius: 15px;
            padding: 20px 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .step:hover .step-content {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            border-color: rgba(93, 206, 240, 0.3);
        }

        .step-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .step-description {
            font-size: 13px;
            line-height: 1.5;
            color: #c0c0c0;
        }
        
        /* Call-to-action */
        .cta-section {
            text-align: center;
            background: linear-gradient(135deg, #5dcef0 0%, #4a9fd1 100%);
            color: #000;
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
        }
        
        .cta-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .cta-button {
            background: #000;
            color: #fff;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 15px;
            transition: background-color 0.3s ease;
        }
        
        .cta-button:hover {
            background: #333;
        }
        
        /* Mobile responsive */
        @media (max-width: 768px) {
            .container {
                margin: 25px auto;
                padding: 0 20px;
                margin-bottom: 120px;
            }
            
            .page-title {
                font-size: 32px;
                margin-bottom: 35px;
            }
            
            .guide-section {
                padding: 25px 20px;
                margin-bottom: 25px;
            }
            
            .section-title {
                font-size: 24px;
                margin-bottom: 20px;
            }
            
            .section-content {
                font-size: 14px;
                line-height: 1.7;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .feature-card {
                padding: 20px;
            }
            
            .feature-title {
                font-size: 18px;
            }
            
            .steps-container {
                flex-direction: column;
                padding: 10px 0;
            }

            /* Hide connecting line on mobile */
            .steps-container::before {
                display: none;
            }

            .step {
                max-width: 100%;
                margin-bottom: 25px;
            }

            .step:last-child {
                margin-bottom: 0;
            }

            .step-number {
                width: 50px;
                height: 50px;
                font-size: 20px;
                margin-bottom: 12px;
            }

            .step-content {
                min-height: auto;
                padding: 15px;
            }

            .step-title {
                font-size: 16px;
            }

            .step-description {
                font-size: 13px;
            }
            
            .cta-section {
                padding: 25px 20px;
            }
            
            .cta-title {
                font-size: 20px;
            }
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 20px auto;
                padding: 0 15px;
                margin-bottom: 130px;
            }
            
            .page-title {
                font-size: 28px;
                margin-bottom: 30px;
            }
            
            .guide-section {
                padding: 20px 15px;
            }
            
            .section-title {
                font-size: 22px;
            }
            
            .section-content {
                font-size: 13px;
            }
            
            .feature-card {
                padding: 15px;
            }
            
            .steps-container {
                padding: 5px 0;
            }

            .step {
                margin-bottom: 20px;
            }

            .step-number {
                width: 45px;
                height: 45px;
                font-size: 18px;
                margin-bottom: 10px;
            }

            .step-content {
                padding: 12px;
            }

            .step-title {
                font-size: 15px;
                margin-bottom: 8px;
            }

            .step-description {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">User Guide</h1>
        
        <!-- Section 1: About URLCHECK -->
        <div class="guide-section">
            <h2 class="section-title">What is URLCHECK?</h2>
            <p class="section-content" style="text-align: center;">
                URLCHECK is a URL security scanning platform designed to protect you from online threats.
                Our machine learning algorithms analyze URLs to detect suspicious redirects, and other security risks before you visit them.
            </p>
            
            <div class="features-grid">
                
                <div class="feature-card">
                    <h3 class="feature-title">📊 Detailed Result</h3>
                    <p class="feature-description">
                        Comprehensive security analysis with threat categorization and mitigation note.
                    </p>
                </div>
                
                <div class="feature-card">
                    <h3 class="feature-title">🔍 Scan History </h3>
                    <p class="feature-description">
                        Keep track of all your scanned URLs with detailed history and easy access to past results.
                    </p>
                </div>
                <div class="feature-card">
                    <h3 class="feature-title">� Customer Support</h3>
                    <p class="feature-description">
                        Get the help you need, when you need it—the team is ready to help.
                    </p>
                </div>
            </div>
        </div>

        <!-- Section 2: How to Use URLCHECK -->
        <div class="guide-section">
            <h2 class="section-title">How to Use URLCHECK</h2>
            <p class="section-content">
                Follow these simple steps to scan any URL and protect yourself from online threats:
            </p>

            <div class="steps-container">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3 class="step-title">Enter the URL</h3>
                        <p class="step-description">
                            Start scanning URL by entering a valid URL in the scan field on main page.                        </p>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3 class="step-title">Start Scanning</h3>
                        <p class="step-description">
                            Press the "Scan URL" button to start the analysis.
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3 class="step-title">Review the Results</h3>
                        <p class="step-description">
                            Within seconds, you'll receive the result clasification, and note about the scanning.
                        </p>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3 class="step-title">Take Action</h3>
                        <p class="step-description">
                            Review the results carefully to decide whether to continue with caution, steer clear of the URL, or implement further security steps.
                        </p>
                    </div>
                </div>
            </div>

            <h3 class="section-title" style="font-size: 24px; margin-top: 40px;">Understanding Your Results</h3>
            <div class="features-grid">
                <div class="feature-card">
                    <h3 class="feature-title" style="color: #4CAF50;">✅ Safe</h3>
                    <p class="feature-description">
                        The URL appears to be legitimate and safe to visit. No security threats detected.
                    </p>
                </div>
                <div class="feature-card">
                    <h3 class="feature-title" style="color: #F44336;">⚠️ Suspicious</h3>
                    <p class="feature-description">
                        The URL shows some warning signs. Proceed with caution and verify the source.
                    </p>
                </div>
            </div>
        </div>

        <!-- Section 3: How to Register Account -->
        <div class="guide-section" id="how-to-register-account">
            <h2 class="section-title">How to Register Account</h2>
            <p class="section-content">
                To create an account and access licensed plan, you need to purchase a token first.
                Follow these step-by-step instructions to register your account:
            </p>

            <div class="steps-container">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3 class="step-title">Visit the Plan Page</h3>
                        <p class="step-description">
                            Navigate to the "Plan" page from the main menu to view available subscription options
                            and purchase a token for account registration.
                        </p>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3 class="step-title">Purchase a Token</h3>
                        <p class="step-description">
                            Select the plan and complete the purchase to receive a token number.
                            This token is required for account registration.
                        </p>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3 class="step-title">Copy Your Token</h3>
                        <p class="step-description">
                            Carefully copy the token number you received after purchase. Make sure to copy the
                            complete token exactly as provided - any errors will prevent registration.
                        </p>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3 class="step-title">Go to Sign Up Page</h3>
                        <p class="step-description">
                            Navigate to the "Sign Up" page and enter your registration details along with
                            the token number you copied from the previous step.
                        </p>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h3 class="step-title">Complete Registration</h3>
                        <p class="step-description">
                            Fill in all required information and paste your token in the token field.
                            Invalid tokens cannot proceed to register account, so ensure accuracy.
                        </p>
                    </div>
                </div>
            </div>

            <div class="features-grid" style="margin-top: 30px;">
                <div class="feature-card">
                    <h3 class="feature-title" style="color: #F44336;">⚠️ Important Note</h3>
                    <p class="feature-description">
                        Make sure you copy the right token exactly as provided. Invalid or incorrect tokens
                        cannot proceed to register account and will result in registration failure.
                    </p>
                </div>
                <div class="feature-card">
                    <h3 class="feature-title" style="color: #5dcef0;">💡 Tip</h3>
                    <p class="feature-description">
                        Keep your token safe and don't share it with others. Each token can only be used
                        once for account registration.
                    </p>
                </div>
            </div>
        </div>


    </div>


</body>
</html>
{% endblock %}
