{% extends "header_admin.html" %}

{% block content %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Training Model</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div, table, tr, th, td {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark theme base styles */
        body {
            background: linear-gradient(135deg, #141719 0%, #121212 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        /* Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
            min-height: calc(100vh - 200px);
        }

        /* Page title */
        .page-title {
            font-family: 'Roboto', sans-serif;
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 48px;
            text-align: center;
            letter-spacing: -0.04em;
        }

        /* Main content container */
        .content {
            width: 100%;
            background-color: transparent;
            margin: 0 auto;
            border-radius: 12px;
            padding: 20px 0;
            box-sizing: border-box;
        }

        /* 2 Cards Per Row Grid Layout */
        .cards-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
            justify-items: center;
            align-items: start;
            width: 100%;
            max-width: 900px;
            margin: 0 auto 30px auto;
        }

        /* Enhanced Card Styles - Properly Positioned */
        .card {
            background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
            border: 1px solid #404040;
            border-radius: 16px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            width: 100%;
            max-width: 380px;
            height: fit-content;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(177, 72, 243, 0.1), transparent);
            transition: left 0.5s;
        }

        .card:hover::before {
            left: 100%;
        }

        .card:hover {
            transform: translateY(-8px) scale(1.02);
            border-color: #b148f3;
            box-shadow: 0 15px 30px rgba(177, 72, 243, 0.3);
        }

        .card-header {
            background: linear-gradient(135deg, #b148f3 0%, #9a3dd9 100%);
            color: #ffffff;
            padding: 18px 20px;
            font-size: 1.2rem;
            font-weight: 700;
            border-bottom: none;
            border-radius: 16px 16px 0 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-icon {
            font-size: 20px;
            opacity: 0.9;
        }

        .card-body {
            padding: 20px;
            color: #b3b3b3;
        }

       /* Form Styles */
       form {
           display: flex;
           flex-direction: column;
           gap: 15px;
       }

       .form-label {
           font-weight: 600;
           color: #ffffff;
           margin-bottom: 8px;
           font-size: 14px;
           letter-spacing: 0.5px;
       }

       .form-control {
           padding: 14px 16px;
           font-size: 16px;
           border: 2px solid #333333;
           border-radius: 12px;
           outline: none;
           transition: all 0.3s ease;
           background-color: #1a1a1a;
           color: #ffffff;
           font-family: inherit;
       }

       .form-control:focus {
           border-color: #b148f3;
           box-shadow: 0 0 0 3px rgba(177, 72, 243, 0.2);
           background-color: #222222;
       }

       .form-control:hover {
           border-color: #555555;
       }

       /* Enhanced Button Styles - Smaller Size */
       .btn {
           display: inline-block;
           padding: 12px 24px;
           font-size: 14px;
           font-weight: 600;
           text-decoration: none;
           border-radius: 25px;
           transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
           border: none;
           cursor: pointer;
           position: relative;
           overflow: hidden;
           text-transform: uppercase;
           letter-spacing: 0.8px;
           min-width: 140px;
           text-align: center;
       }

       .btn::before {
           content: '';
           position: absolute;
           top: 0;
           left: -100%;
           width: 100%;
           height: 100%;
           background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
           transition: left 0.5s;
       }

       .btn:hover::before {
           left: 100%;
       }

       .btn-primary {
           background: linear-gradient(135deg, #b148f3 0%, #9a3dd9 100%);
           color: #ffffff;
           box-shadow: 0 6px 20px rgba(177, 72, 243, 0.4);
       }

       .btn-primary:hover {
           background: linear-gradient(135deg, #9a3dd9 0%, #8a2be2 100%);
           transform: translateY(-3px) scale(1.05);
           box-shadow: 0 10px 25px rgba(177, 72, 243, 0.6);
       }

       .btn-success {
           background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
           color: #ffffff;
           box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
       }

       .btn-success:hover {
           background: linear-gradient(135deg, #1e7e34 0%, #17a2b8 100%);
           transform: translateY(-3px) scale(1.05);
           box-shadow: 0 10px 25px rgba(40, 167, 69, 0.6);
       }

       /* Alert Styles */
       .alert {
           padding: 12px 20px;
           margin-bottom: 15px;
           border-radius: 8px;
           color: #ffffff;
       }

       .alert-dismissible {
           position: relative;
       }

       .alert .btn-close {
           position: absolute;
           top: 10px;
           right: 10px;
           background: none;
           border: none;
           font-size: 1.2rem;
           cursor: pointer;
           color: #ffffff;
       }

       .alert-success {
           background-color: #28a745;
           border: 1px solid #1e7e34;
       }

       .alert-error {
           background-color: #dc3545;
           border: 1px solid #c82333;
       }

       /* Enhanced Table Styling */
       table {
           width: 100%;
           border-collapse: collapse;
           margin-top: 20px;
           font-size: 14px;
           border-radius: 12px;
           overflow: hidden;
           box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
       }

       th {
           background: linear-gradient(135deg, #333333 0%, #2a2a2a 100%);
           color: #ffffff;
           font-weight: 700;
           text-align: left;
           padding: 18px 20px;
           border-bottom: 2px solid #b148f3;
           font-size: 15px;
           letter-spacing: 0.5px;
           text-transform: uppercase;
       }

       td {
           padding: 16px 20px;
           border-bottom: 1px solid #333333;
           color: #b3b3b3;
           vertical-align: middle;
           background-color: rgba(26, 26, 26, 0.8);
           transition: all 0.3s ease;
       }

       tr:hover td {
           background-color: rgba(177, 72, 243, 0.1);
           color: #ffffff;
       }

       tr:last-child td {
           border-bottom: none;
       }

       /* Enhanced Delete button styles */
       .delete-btn {
           background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
           border: none;
           color: white;
           padding: 10px 16px;
           border-radius: 20px;
           cursor: pointer;
           font-size: 13px;
           font-weight: 600;
           transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
           display: inline-flex;
           align-items: center;
           justify-content: center;
           text-transform: uppercase;
           letter-spacing: 0.5px;
           box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
       }

       .delete-btn:hover {
           background: linear-gradient(135deg, #ee5a52 0%, #dc3545 100%);
           transform: translateY(-2px) scale(1.05);
           box-shadow: 0 6px 18px rgba(255, 107, 107, 0.5);
       }

       /* Dataset Files List Enhancement */
       .dataset-files-list ul {
           list-style: none;
           padding: 0;
           margin: 0;
       }

       .dataset-files-list li {
           display: flex;
           justify-content: space-between;
           align-items: center;
           padding: 16px 20px;
           border: 1px solid #333333;
           border-radius: 12px;
           margin-bottom: 12px;
           background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
           transition: all 0.3s ease;
           position: relative;
           overflow: hidden;
       }

       .dataset-files-list li::before {
           content: '';
           position: absolute;
           top: 0;
           left: -100%;
           width: 100%;
           height: 100%;
           background: linear-gradient(90deg, transparent, rgba(177, 72, 243, 0.1), transparent);
           transition: left 0.5s;
       }

       .dataset-files-list li:hover::before {
           left: 100%;
       }

       .dataset-files-list li:hover {
           background: linear-gradient(145deg, #333333 0%, #2a2a2a 100%);
           border-color: #b148f3;
           transform: translateY(-2px);
           box-shadow: 0 8px 20px rgba(177, 72, 243, 0.2);
       }

       .dataset-files-list li span {
           font-size: 15px;
           color: #ffffff;
           font-weight: 500;
           word-break: break-all;
           flex: 1;
           margin-right: 15px;
       }

       .dataset-files-list li form {
           margin: 0;
       }
       /* Responsive Design - 2 Cards Per Row */
       @media (max-width: 1200px) {
           .cards-grid {
               max-width: 800px;
               gap: 25px;
           }
       }

       @media (max-width: 1024px) {
           .cards-grid {
               max-width: 700px;
               gap: 20px;
               padding: 0 20px;
           }

           .container {
               padding: 30px 15px;
           }

           .card {
               max-width: 320px;
           }
       }

       @media (max-width: 768px) {
           .page-title {
               font-size: 36px;
               margin-bottom: 30px;
           }

           .container {
               padding: 20px 15px;
           }

           .cards-grid {
               grid-template-columns: 1fr 1fr;
               gap: 15px;
               max-width: 600px;
               padding: 0 10px;
           }

           .card {
               max-width: 280px;
           }

           .card-header {
               padding: 15px 18px;
               font-size: 1.1rem;
           }

           .card-body {
               padding: 18px;
           }

           .btn {
               padding: 10px 20px;
               font-size: 13px;
               min-width: 120px;
           }

           table {
               font-size: 13px;
           }

           th, td {
               padding: 12px 15px;
           }
       }

       @media (max-width: 480px) {
           .page-title {
               font-size: 28px;
           }

           .cards-grid {
               grid-template-columns: 1fr;
               gap: 20px;
               max-width: 100%;
               padding: 0 10px;
           }

           .card {
               max-width: 100%;
               width: 100%;
           }

           .card-header {
               padding: 15px;
               font-size: 1rem;
           }

           .card-body {
               padding: 15px;
           }

           .btn {
               width: 100%;
               margin-bottom: 10px;
               padding: 12px;
               font-size: 13px;
           }
       }

       /* Loading overlay styles */
       #loading-overlay {
           display: none;
           position: fixed;
           z-index: 100;
           top: 0;
           left: 0;
           width: 100%;
           height: 100%;
           background-color: rgba(0, 0, 0, 0.7);
           align-items: center;
           justify-content: center;
       }

       #loading-container {
           background-color: #282828;
           width: 320px;
           height: 200px;
           border-radius: 12px;
           box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
           display: flex;
           flex-direction: column;
           align-items: center;
           justify-content: center;
           padding: 20px;
           box-sizing: border-box;
           text-align: center;
           border: 1px solid #404040;
       }

       #loading-spinner {
           border: 6px solid #333333;
           border-top: 6px solid #b148f3;
           border-radius: 50%;
           width: 50px;
           height: 50px;
           animation: spin 1s linear infinite;
           margin-bottom: 20px;
       }

       #loading-message {
           font-size: 18px;
           color: #ffffff;
           font-weight: 600;
           margin-bottom: 8px;
       }

       #loading-submessage {
           font-size: 14px;
           color: #b3b3b3;
           font-weight: 400;
       }

       @keyframes spin {
           0% { transform: rotate(0deg);}
           100% { transform: rotate(360deg);}
       }

       /* Responsive loading overlay */
       @media (max-width: 480px) {
           #loading-container {
               width: 280px;
               height: 180px;
               padding: 15px;
           }

           #loading-spinner {
               width: 50px;
               height: 50px;
               margin-bottom: 15px;
           }

           #loading-message {
               font-size: 16px;
           }

           #loading-submessage {
               font-size: 13px;
           }
       }
    </style>

</head>
<body>
    <div class="container">
        <h1 class="page-title">Training Model</h1>

        <div class="content">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
            <div style="margin-bottom: 30px; text-align: center;">
                {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            {% endwith %}

            <!-- Unified Professional Cards Grid -->
            <div class="cards-grid">
                <!-- Upload Dataset Card -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">📁</div>
                        <h3>Upload Dataset</h3>
                    </div>
                    <div class="card-body">
                        <form action="{{ url_for('upload_dataset') }}" method="post" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="dataset" class="form-label">Choose CSV File</label>
                                <input type="file" class="form-control" id="dataset" name="dataset" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Upload Dataset</button>
                        </form>
                    </div>
                </div>

                <!-- Dataset Files Card -->
                {% if is_admin and dataset_files %}
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">📋</div>
                        <h3>Dataset Files</h3>
                    </div>
                    <div class="card-body dataset-files-list">
                        <ul>
                            {% for file in dataset_files %}
                            <li>
                                <span>{{ file }}</span>
                                <form action="{{ url_for('delete_dataset_file', filename=file) }}" method="post" onsubmit="return confirm('Are you sure you want to delete this file?');">
                                    <button type="submit" class="delete-btn">Delete</button>
                                </form>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}

                <!-- Regenerate Training Model Card -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">🤖</div>
                        <h3>Regenerate Training Model</h3>
                    </div>
                    <div class="card-body">
                        <p style="color: #b3b3b3; margin-bottom: 20px; font-size: 14px;">Rebuild the machine learning model with the latest dataset to improve accuracy and performance.</p>
                        <form action="{{ url_for('regenerate_model') }}" method="post" id="regenerate-form">
                            <button type="submit" class="btn btn-success">Regenerate Model</button>
                        </form>
                    </div>
                </div>

                <!-- Training Model Accuracy History Card -->
                {% if trainingmodel_records %}
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">📊</div>
                        <h3>Training Model Accuracy History</h3>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Accuracy (%)</th>
                                    <th>Timestamp</th>
                                    <th>Sender</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in trainingmodel_records %}
                                <tr>
                                    <td style="font-weight: 600; color: #28a745;">{{ record.accuracy }}%</td>
                                    <td>{{ record.timestamp }}</td>
                                    <td>{{ record.sender }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}

                <!-- Show Uploaded Dataset Details -->
                {% if dataset_uploaded %}
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">✅</div>
                        <h3>Uploaded Dataset Details</h3>
                    </div>
                    <div class="card-body">
                        <p style="color: #ffffff; font-weight: 600; margin-bottom: 10px;"><strong>Uploaded File:</strong> {{ dataset_uploaded }}</p>
                        <p style="color: #b3b3b3;">The dataset has been successfully uploaded and is ready for processing.</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Loading overlay -->
    <div id="loading-overlay">
        <div id="loading-container">
            <div id="loading-spinner"></div>
            <div id="loading-message">Regenerating Model...</div>
            <div id="loading-submessage">This may take a few minutes</div>
        </div>
    </div>

    <script>
        // Show loading overlay on regenerate model form submit
        document.addEventListener('DOMContentLoaded', function() {
            const regenerateForm = document.getElementById('regenerate-form');
            if (regenerateForm) {
                regenerateForm.addEventListener('submit', function(event) {
                    document.getElementById('loading-overlay').style.display = 'flex';
                    const submitButton = regenerateForm.querySelector('button[type="submit"]');
                    submitButton.disabled = true;
                    submitButton.textContent = 'Processing...';
                });
            }
        });
    </script>

 <p style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; bottom: 0px;  margin-bottom: 15px; width: 100%;">© URLCHECK 2025.All rights reserved</p>
</body>
</html>
{% endblock %}
