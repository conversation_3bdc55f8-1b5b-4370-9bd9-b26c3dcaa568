-- Seed data for URL Scanner Database
USE urlscanner;

-- Insert default admin user
-- Password: admin123 (plain text as per original code)
INSERT INTO `admin` (`username`, `password`, `email`) VALUES
('admin', 'admin123', '<EMAIL>')
ON DUPLICATE KEY UPDATE 
    `password` = VALUES(`password`),
    `email` = VALUES(`email`);

-- Insert some sample tokens for user registration
INSERT INTO `token` (`token_number`) VALUES
('TOKEN001'),
('TOKEN002'),
('TOKEN003'),
('TOKEN004'),
('TOKEN005'),
('DEMO001'),
('DEMO002'),
('DEMO003'),
('TEST001'),
('TEST002')
ON DUPLICATE KEY UPDATE 
    `token_number` = VALUES(`token_number`);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_token ON users(token);
CREATE INDEX IF NOT EXISTS idx_scan_results_url ON scan_results(url(255));
CREATE INDEX IF NOT EXISTS idx_scan_results_domain ON scan_results(domain);
CREATE INDEX IF NOT EXISTS idx_url_breakdown_scan_url ON url_breakdown(scan_url(255));

-- Set proper permissions and optimize tables
OPTIMIZE TABLE admin;
OPTIMIZE TABLE users;
OPTIMIZE TABLE token;
OPTIMIZE TABLE contact_us;
OPTIMIZE TABLE scan_results;
OPTIMIZE TABLE url_breakdown;
