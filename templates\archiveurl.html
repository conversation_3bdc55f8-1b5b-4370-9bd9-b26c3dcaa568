{% if is_admin %}
    {% extends "header_admin.html" %}
{% else %}
    {% extends "header.html" %}
{% endif %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scan History</title>
    <style>
        /* Reset and base styles */
        body, h1, table, tr, th, td {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}

        /* Main container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        /* Page title */
        .page-title {
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 48px;
            text-align: center;
            letter-spacing: -0.04em;

        }

        /* Table styling */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 14px;

        }

        th {
            background-color: #282828;
            color: #ffffff;
            font-weight: 600;
            text-align: left;
            padding: 15px;
            border-bottom: 2px solid #333333;
        }

        td {
            padding: 15px;
            border-bottom: 1px solid #333333;
            color: #b3b3b3;
            vertical-align: middle;
        }

        tr:hover {
            background-color: #222222;
        }

        tr:last-child td {
            border-bottom: none;
        }

        /* URL column with truncation */
        .url-cell {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Classification styling */
        .classification {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 80px;
        }

        .safe {
            background-color: #1DB954; /* Spotify green */
            color: #000000;
        }

        .suspicious {
            background-color: #E91429; /* Spotify red */
            color: #ffffff;
        }

        /* Center align the classification column */
        td[data-label="Classification"] {
            text-align: center;
        }

        /* Center align the date column */
        td[data-label="Date"] {
            text-align: center;
        }

        /* Detail button */
        .detail-button {
            display: inline-block;
            padding: 8px 15px;
            background-color: #333333;
            color: #ffffff;
            border: none;
            border-radius: 30px;
            font-size: 12px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            text-align: center;
            line-height: 1.4;
        }

        .detail-button:hover {
            background-color: #4aa4c0; /* Spotify green */
            color: #000000;
            transform: scale(1.05);
        }

        /* Top controls container */
        .top-controls {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 20px;
            gap: 20px;
        }

        .filter-section {
            flex: 0 0 auto;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Minimalist search input */
        .search-input {
            width: 200px;
            padding: 8px 12px;
            border: 1px solid #333333;
            border-radius: 6px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #4aa4c0;
            box-shadow: 0 0 0 1px rgba(74, 164, 192, 0.3);
        }

        .search-input::placeholder {
            color: #666666;
            font-style: italic;
        }

        .search-clear-btn {
            background: none;
            border: none;
            color: #666666;
            cursor: pointer;
            font-size: 14px;
            padding: 4px 6px;
            border-radius: 3px;
            transition: all 0.3s ease;
            display: none;
            margin-left: -25px;
            position: relative;
            z-index: 1;
        }

        .search-clear-btn:hover {
            color: #4aa4c0;
            background-color: rgba(74, 164, 192, 0.1);
        }

        .search-clear-btn.show {
            display: block;
        }

        /* Classification filter dropdown */
        .classification-filter {
            padding: 8px 12px;
            border: 1px solid #333333;
            border-radius: 6px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 13px;
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 120px;
        }

        .classification-filter:focus {
            outline: none;
            border-color: #4aa4c0;
            box-shadow: 0 0 0 1px rgba(74, 164, 192, 0.3);
        }

        .classification-filter option {
            background-color: #1a1a1a;
            color: #ffffff;
            padding: 8px;
        }

        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 40px 0;
            color: #b3b3b3;
            font-style: italic;
        }

        /* Responsive design for search */
        @media (max-width: 768px) {
            .top-controls {
                justify-content: center;
                flex-direction: column;
                gap: 10px;
            }

            .filter-section {
                flex-direction: column;
                gap: 10px;
            }

            .search-input {
                width: 180px;
            }

            .classification-filter {
                min-width: 100px;
            }
        }

        @media (max-width: 480px) {
            .filter-section {
                gap: 8px;
            }

            .search-input {
                width: 160px;
                font-size: 12px;
                padding: 6px 10px;
            }

            .classification-filter {
                font-size: 12px;
                padding: 6px 10px;
                min-width: 90px;
            }
        }

        /* Standardized Mobile Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 0 20px;
                margin-bottom: 120px;
            }

            .page-title {
                font-size: 32px;
                margin-bottom: 30px;
                margin-top: 50px;
            }

            table, thead, tbody, th, td, tr {
                display: block;
            }

            thead tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
            }

            tr {
                margin-bottom: 15px;
                background-color: #222222;
                border-radius: 8px;
                overflow: hidden;
            }

            td {
                position: relative;
                padding-left: 50%;
                text-align: center;
                border-bottom: 1px solid #333333;
            }

            td:before {
                position: absolute;
                top: 15px;
                left: 15px;
                width: 45%;
                padding-right: 10px;
                white-space: nowrap;
                text-align: center;
                font-weight: bold;
                content: attr(data-label);
                color: #ffffff;
            }

            td:nth-of-type(1):before { content: "No."; }
            td:nth-of-type(2):before { content: "URL"; }
            td:nth-of-type(3):before { content: "Classification"; }
            td:nth-of-type(4):before { content: "Note"; }
            td:nth-of-type(5):before { content: "Date"; }
            td:nth-of-type(6):before { content: "Detail"; }

            .url-cell {
                max-width: none;
                white-space: normal;
                word-break: break-all;
            }
        }

        @media (max-width: 600px) {
            .container {
                margin: 15px auto;
                padding: 0 15px;
                margin-bottom: 100px;
            }

            .page-title {
                font-size: 28px;
                margin-bottom: 25px;
            }

            td {
                padding: 12px 15px 12px 45%;
                font-size: 14px;
            }

            td:before {
                top: 12px;
                left: 12px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px auto;
                padding: 0 10px;
                margin-bottom: 80px;
            }

            .page-title {
                font-size: 24px;
                margin-bottom: 20px;
            }

            td {
                padding: 10px 12px 10px 40%;
                font-size: 13px;
            }

            td:before {
                top: 10px;
                left: 10px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">Scan History</h1>

        <!-- Top Controls: Classification Filter and Search -->
        <div class="top-controls">
            <div class="filter-section">
                <select id="classification_filter" class="classification-filter">
                    <option value="">All Classifications</option>
                    <option value="Safe" {{ 'selected' if classification_filter == 'Safe' else '' }}>Safe</option>
                    <option value="Suspicious" {{ 'selected' if classification_filter == 'Suspicious' else '' }}>Suspicious</option>
                </select>

                <div class="search-section">
                    <input type="text"
                           id="search_url"
                           class="search-input"
                           value="{{ search_url or '' }}"
                           placeholder="Search by URL..."
                           autocomplete="off">
                    <button type="button" id="clearSearch" class="search-clear-btn" title="Clear search">✕</button>
                </div>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th>No.</th>
                    <th>URL</th>
                    <th style="text-align: center;">Classification</th>
                    <th>Note</th>
                    <th style="text-align: center;">Date</th>
                    <th style="text-align: center;">Detail</th>
                </tr>
            </thead>
            <tbody>
                {% for row in results %}
                <tr>
                    <td data-label="No.">{{ loop.index }}</td>
                    <td data-label="URL" class="url-cell">{{ row.url }}</td>
                    <td data-label="Classification">
                        <span class="classification {% if row.classification == 'Safe' %}safe{% else %}suspicious{% endif %}">
                            {{ row.classification }}
                        </span>
                    </td>
                    <td data-label="Note">{{ row.note }}</td> 
                    <td data-label="Date">{{ row.timestamp.strftime('%d.%m.%Y %H:%M') if row.timestamp else 'N/A' }}</td>
                    <td data-label="Detail">
                        <a href="{{ url_for('details_by_url', url_encoded=row.url|urlencode) }}" class="detail-button">View Details</a>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="6" class="empty-state">
                        {% if search_url or classification_filter %}
                            No URLs found matching your filters
                            {% if search_url %} containing "{{ search_url }}"{% endif %}
                            {% if classification_filter %} with classification "{{ classification_filter }}"{% endif %}.
                            Try different filters or <a href="{{ url_for('archiveurl') }}" style="color: #4aa4c0; text-decoration: underline;">clear all filters</a> to view all your scan history.
                        {% else %}
                            No scan history found. Try scanning some URLs first.
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <script>
        // Real-time search functionality
        let searchTimeout;
        const searchInput = document.getElementById('search_url');
        const clearButton = document.getElementById('clearSearch');
        const classificationFilter = document.getElementById('classification_filter');

        // Show/hide clear button based on input content
        function toggleClearButton() {
            if (searchInput.value.trim()) {
                clearButton.classList.add('show');
            } else {
                clearButton.classList.remove('show');
            }
        }

        // Perform search with debouncing
        function performSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const searchTerm = searchInput.value.trim();
                const classificationValue = classificationFilter.value;
                const currentUrl = new URL(window.location);

                if (searchTerm) {
                    currentUrl.searchParams.set('search_url', searchTerm);
                } else {
                    currentUrl.searchParams.delete('search_url');
                }

                if (classificationValue) {
                    currentUrl.searchParams.set('classification_filter', classificationValue);
                } else {
                    currentUrl.searchParams.delete('classification_filter');
                }

                // Navigate to the new URL
                window.location.href = currentUrl.toString();
            }, 500); // 500ms delay for debouncing
        }

        // Clear search
        function clearSearch() {
            searchInput.value = '';
            classificationFilter.value = '';
            toggleClearButton();
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('search_url');
            currentUrl.searchParams.delete('classification_filter');
            window.location.href = currentUrl.toString();
        }

        // Event listeners
        searchInput.addEventListener('input', function() {
            toggleClearButton();
            performSearch();
        });

        classificationFilter.addEventListener('change', function() {
            performSearch();
        });

        clearButton.addEventListener('click', clearSearch);

        // Initialize clear button visibility on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleClearButton();
        });
    </script>

</body>
</html>
{% endblock %}
