#!/bin/bash

# URL Scanner Docker Deployment Script
# This script helps deploy the URL Scanner application using Docker

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is installed and running
check_docker() {
    print_status "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are available"
}

# Function to check if .env file exists
check_env_file() {
    print_status "Checking environment configuration..."
    
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from .env.example..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_warning "Please edit .env file with your configuration before continuing."
            print_warning "Press Enter to continue after editing .env file..."
            read
        else
            print_error ".env.example file not found. Please create .env file manually."
            exit 1
        fi
    fi
    
    print_success "Environment configuration found"
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p static/uploads
    
    print_success "Directories created"
}

# Function to build and start services
deploy_services() {
    print_status "Building and starting services..."
    
    # Stop existing containers if running
    docker-compose down
    
    # Build and start services
    docker-compose up --build -d
    
    print_success "Services started successfully"
}

# Function to wait for services to be healthy
wait_for_services() {
    print_status "Waiting for services to be healthy..."
    
    # Wait for MySQL to be ready
    print_status "Waiting for MySQL to be ready..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose exec mysql mysqladmin ping -h localhost --silent; then
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "MySQL failed to start within 60 seconds"
        exit 1
    fi
    
    # Wait for web service to be ready
    print_status "Waiting for web service to be ready..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:5000/ &> /dev/null; then
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "Web service failed to start within 60 seconds"
        exit 1
    fi
    
    print_success "All services are healthy"
}

# Function to show service status
show_status() {
    print_status "Service Status:"
    docker-compose ps
    
    echo ""
    print_success "Deployment completed successfully!"
    echo ""
    print_status "Access URLs:"
    echo "  - URL Scanner Application: http://localhost:5000"
    echo "  - phpMyAdmin (Database): http://localhost:8080"
    echo ""
    print_status "Default Admin Credentials:"
    echo "  - Username: admin"
    echo "  - Password: admin123"
    echo ""
    print_warning "Please change the default admin password after first login!"
}

# Main deployment function
main() {
    echo "=========================================="
    echo "    URL Scanner Docker Deployment"
    echo "=========================================="
    echo ""
    
    check_docker
    check_env_file
    create_directories
    deploy_services
    wait_for_services
    show_status
}

# Run main function
main
