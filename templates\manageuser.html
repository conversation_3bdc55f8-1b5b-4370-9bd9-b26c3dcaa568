{% extends "header_admin.html" %}

{% block content %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark theme base styles */
        body {
            background: linear-gradient(135deg, #141719 0%, #121212 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        /* Input field styling for dark mode */
        input[type="text"],
        input[type="text"]:focus,
        input[type="text"]:active,
        input[type="text"]:hover,
        input[type="password"],
        input[type="password"]:focus,
        input[type="email"],
        input[type="email"]:focus,
        textarea,
        textarea:focus,
        select,
        select:focus {
            background-color: #333333 !important;
            color: #ffffff !important;
            border: 1px solid #444444;
            outline-color: #b148f3;
        }

        /* Override browser autofill styles */
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
            -webkit-text-fill-color: #ffffff !important;
            transition: background-color 5000s ease-in-out 0s;
        }

        /* Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
            min-height: calc(100vh - 200px);
        }

        /* Page title */
        .page-title {
            font-family: 'Roboto', sans-serif;
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 48px;
            text-align: center;
            letter-spacing: -0.04em;
        }

        /* Main content container */
        .content {
            width: 100%;
            background-color: transparent;
            margin: 0 auto;
            border-radius: 12px;
            padding: 20px 0;
            box-sizing: border-box;
        }

        /* User Cards Grid Layout */
        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 30px;
            margin-top: 30px;
            padding: 0 20px;
        }

        /* Table Styling */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 14px;
        }

        th {
            background-color: #282828;
            color: #ffffff;
            font-weight: 600;
            text-align: left;
            padding: 15px;
            border-bottom: 2px solid #333333;
        }

        td {
            padding: 15px;
            border-bottom: 1px solid #333333;
            color: #b3b3b3;
            vertical-align: middle;
        }

        tr:hover {
            background-color: #222222;
        }

        tr:last-child td {
            border-bottom: none;
        }

        /* Profile Picture in Table */
        .table-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid #b148f3;
            overflow: hidden;
            position: relative;
            background: linear-gradient(135deg, #b148f3 0%, #9a3dd9 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }

        .table-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .table-avatar .no-image {
            color: #ffffff;
            font-size: 10px;
            font-weight: 600;
            text-align: center;
        }

        /* Status styling - matching generate_token format */
        .status-active {
            color: #28a745;
            font-weight: 600;
        }

        .status-expired {
            color: #dc3545;
            font-weight: 600;
        }

        .status-inactive {
            color: #6c757d;
            font-weight: 600;
        }

        /* Detail button styling - matching admin_archiveurl */
        .detail-button {
            display: inline-block;
            padding: 8px 15px;
            background-color: #333333;
            color: #ffffff;
            border: none;
            border-radius: 30px;
            font-size: 12px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            text-align: center;
            line-height: 1.4;
            cursor: pointer;
        }

        .detail-button:hover {
            background-color: #b148f3;
            color: #000000;
            transform: scale(1.05);
            text-decoration: none;
        }

        /* No Users Message */
        .no-users {
            text-align: center;
            color: #b3b3b3;
            font-size: 18px;
            margin-top: 60px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px dashed #404040;
        }

        /* Modal Popup Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
            margin: 8% auto;
            padding: 25px;
            border: 1px solid #404040;
            border-radius: 16px;
            width: 85%;
            max-width: 420px;
            position: relative;
            bottom: 70px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .close {
            color: #b3b3b3;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #b148f3;
        }

        .modal-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .modal-header h2 {
            color: #ffffff;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .modal-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 15px;
            border: 3px solid #b148f3;
            overflow: hidden;
            background: linear-gradient(135deg, #b148f3 0%, #9a3dd9 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .modal-info {
            margin-bottom: 20px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .info-label {
            color: #b3b3b3;
            font-weight: 500;
            min-width: 120px;
        }

        .info-value {
            color: #ffffff;
            font-weight: 600;
            flex: 1;
            text-align: right;
        }

        /* Editable input fields in modal */
        .info-input {
            background-color: #333333;
            color: #ffffff;
            border: 1px solid #444444;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            width: 100%;
            max-width: 200px;
            transition: border-color 0.3s ease;
        }

        .info-input:focus {
            border-color: #b148f3;
            outline: none;
            box-shadow: 0 0 0 2px rgba(177, 72, 243, 0.2);
        }

        .info-input:disabled {
            background-color: #222222;
            color: #888888;
            cursor: not-allowed;
        }

        /* Edit mode styles */
        .edit-mode .info-row.editable {
            background: rgba(177, 72, 243, 0.1);
            border: 1px solid rgba(177, 72, 243, 0.3);
        }

        .edit-mode .info-row.readonly {
            opacity: 0.7;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            text-align: center;
        }

        .btn-edit {
            background: linear-gradient(135deg, #b148f3 0%, #9a3dd9 100%);
            color: #ffffff;
        }

        .btn-edit:hover {
            background: linear-gradient(135deg, #9a3dd9 0%, #8a2be2 100%);
            transform: translateY(-2px);
        }

        .btn-delete {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: #ffffff;
        }

        .btn-delete:hover {
            background: linear-gradient(135deg, #ee5a52 0%, #dc3545 100%);
            transform: translateY(-2px);
        }

        .btn-save {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: #ffffff;
        }

        .btn-save:hover {
            background: linear-gradient(135deg, #1e7e34 0%, #17a2b8 100%);
            transform: translateY(-2px);
        }

        .btn-cancel {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: #ffffff;
        }

        .btn-cancel:hover {
            background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
            transform: translateY(-2px);
        }

        .btn-remove-image {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #000000;
        }

        .btn-remove-image:hover {
            background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
            transform: translateY(-2px);
        }

        /* Hide/show buttons based on mode */
        .view-mode .edit-buttons {
            display: flex;
        }

        .view-mode .save-buttons {
            display: none;
        }

        .edit-mode .edit-buttons {
            display: none;
        }

        .edit-mode .save-buttons {
            display: flex;
        }

        /* Loading state */
        .btn.loading {
            opacity: 0.7;
            cursor: not-allowed;
            position: relative;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Top controls container */
        .top-controls {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 20px;
            gap: 20px;
        }

        .search-section {
            flex: 0 0 auto;
            display: flex;
            align-items: center;
            gap: 82px;
        }

        /* Top controls container - matching admin_archiveurl */
        .top-controls {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 20px;
            gap: 20px;
        }

        .search-section {
            flex: 0 0 auto;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Minimalist search input */
        .search-input {
            width: 250px;
            padding: 8px 12px;
            border: 1px solid #333333;
            border-radius: 6px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #4aa4c0;
            box-shadow: 0 0 0 1px rgba(74, 164, 192, 0.3);
        }

        .search-input::placeholder {
            color: #666666;
            font-style: italic;
        }

        .search-clear-btn {
            background: none;
            border: none;
            color: #666666;
            cursor: pointer;
            font-size: 14px;
            padding: 4px 6px;
            border-radius: 3px;
            transition: all 0.3s ease;
            display: none;
            margin-left: -25px;
            position: relative;
            z-index: 1;
        }

        .search-clear-btn:hover {
            color: #4aa4c0;
            background-color: rgba(74, 164, 192, 0.1);
        }

        .search-clear-btn.show {
            display: block;
        }

        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 40px 0;
            color: #b3b3b3;
            font-style: italic;
        }
        /* Responsive design */
        @media (max-width: 768px) {
            .page-title {
                font-size: 36px;
                margin-bottom: 30px;
            }

            .container {
                padding: 20px 15px;
            }

            .top-controls {
                justify-content: center;
                margin-bottom: 15px;
            }

            .search-input {
                width: 100%;
                max-width: 300px;
                padding: 10px 40px 10px 12px;
                font-size: 13px;
            }

            /* Mobile table styling */
            table, thead, tbody, th, td, tr {
                display: block;
            }

            thead tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
            }

            tr {
                border: 1px solid #333333;
                margin-bottom: 10px;
                padding: 10px;
                border-radius: 8px;
                background-color: #1e1e1e;
            }

            td {
                border: none;
                border-bottom: 1px solid #333333;
                position: relative;
                padding-left: 50%;
                padding-top: 10px;
                padding-bottom: 10px;
            }

            td:before {
                content: attr(data-label) ": ";
                position: absolute;
                left: 6px;
                width: 45%;
                padding-right: 10px;
                white-space: nowrap;
                color: #b148f3;
                font-weight: 600;
            }

            /* Hide profile picture column label on mobile */
            td[data-label="Profile Picture"]:before {
                display: none;
            }

            /* Center profile picture on mobile */
            td[data-label="Profile Picture"] {
                text-align: center;
                padding-left: 15px;
            }

            .modal-content {
                width: 90%;
                margin: 12% auto;
                padding: 18px;
                max-width: 350px;
            }

            .modal-actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }

            .search-results-info {
                margin: 0 15px 20px 15px;
                padding: 12px 15px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .users-grid {
                grid-template-columns: 1fr;
            }

            .page-title {
                font-size: 28px;
            }



            .search-input {
                padding: 8px 35px 8px 10px;
                font-size: 12px;
            }

            .search-clear-btn {
                right: 8px;
                width: 20px;
                height: 20px;
                font-size: 14px;
            }
        }
</style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">User Management</h1>
        <div class="content">
            <!-- Top Controls: Search -->
            <div class="top-controls">
                <div class="search-section">
                    <input type="text"
                           id="search_query"
                           class="search-input"
                           value="{{ search_query or '' }}"
                           placeholder="Search by username or email..."
                           autocomplete="off">
                    <button type="button" id="clearSearch" class="search-clear-btn" title="Clear search">✕</button>
                </div>
            </div>



            {% if users %}
                <table>
                    <thead>
                        <tr>
                            <th>No.</th>
                            <th style="text-align: center;">Profile Picture</th>
                            <th>Username</th>
                            <th>Gmail</th>
                            <th style="text-align: center;">Status</th>
                            <th style="text-align: center;">Detail</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td data-label="No.">{{ loop.index }}</td>
                            <td data-label="Profile Picture" style="text-align: center;">
                                <div class="table-avatar">
                                    {% if user.profile_picture %}
                                        <img src="data:image/jpeg;base64,{{ user.profile_picture | b64encode }}" alt="Profile Picture" />
                                    {% else %}
                                        <div class="no-image">No Image</div>
                                    {% endif %}
                                </div>
                            </td>
                            <td data-label="Username">{{ user.username }}</td>
                            <td data-label="Gmail">{{ user.email }}</td>
                            <td data-label="Status" style="text-align: center;">
                                {% if user.status == 'Active' %}
                                    <span class="status-active">{{ user.status }}</span>
                                {% elif user.status == 'Expired' %}
                                    <span class="status-expired">{{ user.status }}</span>
                                {% else %}
                                    <span class="status-inactive">{{ user.status }}</span>
                                {% endif %}
                            </td>
                            <td data-label="Detail" style="text-align: center;">
                                <button class="detail-button"
                                        onclick="openUserModal('{{ user.id }}', '{{ user.username }}', '{{ user.email }}', '{{ user.token_number if user.token_number else '' }}', '{{ user.start_date.strftime('%d.%m.%Y') if user.start_date else '' }}', '{{ user.expiry_date.strftime('%d.%m.%Y') if user.expiry_date else '' }}', '{% if user.profile_picture %}data:image/jpeg;base64,{{ user.profile_picture | b64encode }}{% endif %}')">
                                    View Details
                                </button>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="empty-state">
                                {% if search_query %}
                                    No users found matching "{{ search_query }}". Try a different search term or <a href="{{ url_for('manage_user') }}" style="color: #b148f3; text-decoration: underline;">clear the search</a> to view all users.
                                {% else %}
                                    No users found in the system.
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <table>
                    <thead>
                        <tr>
                            <th>No.</th>
                            <th style="text-align: center;">Profile Picture</th>
                            <th>Username</th>
                            <th>Gmail</th>
                            <th style="text-align: center;">Status</th>
                            <th style="text-align: center;">Detail</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="6" class="empty-state">
                                {% if search_query %}
                                    No users found matching "{{ search_query }}". Try a different search term or <a href="{{ url_for('manage_user') }}" style="color: #b148f3; text-decoration: underline;">clear the search</a> to view all users.
                                {% else %}
                                    No users found in the system.
                                {% endif %}
                            </td>
                        </tr>
                    </tbody>
                </table>
            {% endif %}
        </div>
    </div>

    <!-- User Details Modal -->
    <div id="userModal" class="modal view-mode">
        <div class="modal-content">
            <span class="close" onclick="closeUserModal()">&times;</span>
            <div class="modal-header">
                <div class="modal-avatar" id="modalAvatar">
                    <div class="no-image">No Image</div>
                </div>
                <h2 id="modalUsername">Username</h2>
            </div>
            <div class="modal-info">
                <div class="info-row readonly">
                    <span class="info-label">User ID:</span>
                    <span class="info-value" id="modalUserId">-</span>
                </div>
                <div class="info-row readonly">
                    <span class="info-label">Email:</span>
                    <span class="info-value" id="modalEmail">-</span>
                </div>
                <div class="info-row editable">
                    <span class="info-label">Username:</span>
                    <span class="info-value">
                        <span id="modalUsernameDisplay">-</span>
                        <input type="text" id="modalUsernameInput" class="info-input" style="display: none;">
                    </span>
                </div>
                <div class="info-row editable">
                    <span class="info-label">Token Number:</span>
                    <span class="info-value">
                        <span id="modalTokenDisplay">-</span>
                        <input type="text" id="modalTokenInput" class="info-input" style="display: none;" placeholder="Enter token number">
                    </span>
                </div>
                <div class="info-row readonly">
                    <span class="info-label">Start Date:</span>
                    <span class="info-value" id="modalStartDate">-</span>
                </div>
                <div class="info-row readonly">
                    <span class="info-label">Expiry Date:</span>
                    <span class="info-value" id="modalExpiryDate">-</span>
                </div>
            </div>
            <div class="modal-actions">
                <div class="edit-buttons">
                    <button class="btn btn-edit" onclick="enableEditMode()">Edit User</button>
                    <button class="btn btn-remove-image" onclick="removeProfileImage()" id="removeImageBtn" style="display: none;">Remove Image</button>
                    <button class="btn btn-delete" onclick="deleteUser()">Delete User</button>
                </div>
                <div class="save-buttons">
                    <button class="btn btn-save" onclick="saveUserChanges()">Save Changes</button>
                    <button class="btn btn-cancel" onclick="cancelEdit()">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 400px;">
            <span class="close" onclick="closeDeleteModal()">&times;</span>
            <div class="modal-header">
                <h2>Confirm Delete User</h2>
            </div>
            <div class="modal-info">
                <p style="color: #b3b3b3; margin-bottom: 20px; text-align: center;">
                    Are you sure you want to delete user <strong id="deleteUsername"></strong>?
                    <br><br>
                    <span style="color: #ff6b6b;">This action cannot be undone.</span>
                </p>
                <div class="info-row">
                    <span class="info-label">Admin Password:</span>
                    <span class="info-value">
                        <input type="password" id="deleteAdminPassword" class="info-input" placeholder="Enter admin password" style="width: 100%; max-width: none;">
                    </span>
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-delete" onclick="confirmDeleteUser()">Confirm Delete</button>
                <button class="btn btn-cancel" onclick="closeDeleteModal()">Cancel</button>
            </div>
        </div>
    </div>

    <script>
        // Real-time search functionality
        let searchTimeout;
        const searchInput = document.getElementById('search_query');
        const clearButton = document.getElementById('clearSearch');

        // Show/hide clear button based on input content
        function toggleClearButton() {
            if (searchInput.value.trim()) {
                clearButton.classList.add('show');
            } else {
                clearButton.classList.remove('show');
            }
        }

        // Perform search with debouncing
        function performSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const searchTerm = searchInput.value.trim();
                const currentUrl = new URL(window.location);

                if (searchTerm) {
                    currentUrl.searchParams.set('search_query', searchTerm);
                } else {
                    currentUrl.searchParams.delete('search_query');
                }

                // Navigate to the new URL
                window.location.href = currentUrl.toString();
            }, 500); // 500ms delay for debouncing
        }

        // Clear search
        function clearSearch() {
            searchInput.value = '';
            toggleClearButton();
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('search_query');
            window.location.href = currentUrl.toString();
        }

        // Event listeners for search
        searchInput.addEventListener('input', function() {
            toggleClearButton();
            performSearch();
        });

        clearButton.addEventListener('click', clearSearch);

        // Initialize clear button visibility on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleClearButton();
        });

        // User management functionality
        let currentUserId = null;
        let originalUserData = {};



        function openUserModal(userId, username, email, tokenNumber, startDate, expiryDate, profilePicture) {
            currentUserId = userId;

            // Store original data for cancel functionality
            originalUserData = {
                username: username,
                tokenNumber: tokenNumber,
                startDate: startDate,
                expiryDate: expiryDate
            };

            // Set modal content
            document.getElementById('modalUserId').textContent = userId;
            document.getElementById('modalUsername').textContent = username;
            document.getElementById('modalEmail').textContent = email;

            // Set editable fields
            document.getElementById('modalUsernameDisplay').textContent = username;
            document.getElementById('modalUsernameInput').value = username;
            document.getElementById('modalTokenDisplay').textContent = tokenNumber || 'No Token';
            document.getElementById('modalTokenInput').value = tokenNumber || '';

            // Set readonly fields
            document.getElementById('modalStartDate').textContent = startDate || 'Not Set';
            document.getElementById('modalExpiryDate').textContent = expiryDate || 'Not Set';

            // Set profile picture and show/hide remove button
            const modalAvatar = document.getElementById('modalAvatar');
            const removeImageBtn = document.getElementById('removeImageBtn');
            if (profilePicture && profilePicture.trim() !== '') {
                modalAvatar.innerHTML = '<img src="' + profilePicture + '" alt="Profile Picture" />';
                removeImageBtn.style.display = 'inline-block';
            } else {
                modalAvatar.innerHTML = '<div class="no-image">No Image</div>';
                removeImageBtn.style.display = 'none';
            }

            // Reset to view mode
            setViewMode();

            // Show modal
            document.getElementById('userModal').style.display = 'block';
        }

        function setViewMode() {
            const modal = document.getElementById('userModal');
            modal.className = 'modal view-mode';

            // Show display elements, hide input elements
            document.getElementById('modalUsernameDisplay').style.display = 'inline';
            document.getElementById('modalUsernameInput').style.display = 'none';
            document.getElementById('modalTokenDisplay').style.display = 'inline';
            document.getElementById('modalTokenInput').style.display = 'none';
        }

        function setEditMode() {
            const modal = document.getElementById('userModal');
            modal.className = 'modal edit-mode';

            // Hide display elements, show input elements
            document.getElementById('modalUsernameDisplay').style.display = 'none';
            document.getElementById('modalUsernameInput').style.display = 'inline';
            document.getElementById('modalTokenDisplay').style.display = 'none';
            document.getElementById('modalTokenInput').style.display = 'inline';
        }

        function enableEditMode() {
            setEditMode();
        }

        function cancelEdit() {
            // Restore original values
            document.getElementById('modalUsernameInput').value = originalUserData.username;
            document.getElementById('modalTokenInput').value = originalUserData.tokenNumber || '';
            document.getElementById('modalUsernameDisplay').textContent = originalUserData.username;
            document.getElementById('modalTokenDisplay').textContent = originalUserData.tokenNumber || 'No Token';

            setViewMode();
        }

        function saveUserChanges() {
            const newUsername = document.getElementById('modalUsernameInput').value.trim();
            const newToken = document.getElementById('modalTokenInput').value.trim();

            if (!newUsername) {
                alert('Username cannot be empty');
                return;
            }

            // Show loading state
            const saveBtn = document.querySelector('.btn-save');
            saveBtn.classList.add('loading');
            saveBtn.disabled = true;

            // Prepare data for update
            const updateData = {
                user_id: currentUserId,
                username: newUsername,
                token_number: newToken || null
            };

            // Send AJAX request to update user
            fetch('/update_user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            })
            .then(response => response.json())
            .then(data => {
                saveBtn.classList.remove('loading');
                saveBtn.disabled = false;

                if (data.success) {
                    // Update display values
                    document.getElementById('modalUsernameDisplay').textContent = newUsername;
                    document.getElementById('modalTokenDisplay').textContent = newToken || 'No Token';
                    document.getElementById('modalUsername').textContent = newUsername;

                    // Update dates if provided
                    if (data.start_date) {
                        document.getElementById('modalStartDate').textContent = data.start_date;
                    }
                    if (data.expiry_date) {
                        document.getElementById('modalExpiryDate').textContent = data.expiry_date;
                    }

                    // Update original data
                    originalUserData.username = newUsername;
                    originalUserData.tokenNumber = newToken;
                    originalUserData.startDate = data.start_date || originalUserData.startDate;
                    originalUserData.expiryDate = data.expiry_date || originalUserData.expiryDate;

                    setViewMode();

                    // Refresh the page to update the card
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    alert('Error: ' + (data.message || 'Failed to update user'));
                }
            })
            .catch(error => {
                saveBtn.classList.remove('loading');
                saveBtn.disabled = false;
                console.error('Error:', error);
                alert('An error occurred while updating the user');
            });
        }

        function removeProfileImage() {
            if (!confirm('Are you sure you want to remove this user\'s profile image?')) {
                return;
            }

            // Show loading state
            const removeBtn = document.getElementById('removeImageBtn');
            removeBtn.classList.add('loading');
            removeBtn.disabled = true;

            // Send AJAX request to remove profile image
            fetch('/admin/remove_user_profile_image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: currentUserId
                })
            })
            .then(response => response.json())
            .then(data => {
                removeBtn.classList.remove('loading');
                removeBtn.disabled = false;

                if (data.success) {
                    // Update the modal avatar display
                    const modalAvatar = document.getElementById('modalAvatar');
                    modalAvatar.innerHTML = '<div class="no-image">No Image</div>';

                    // Hide the remove button
                    removeBtn.style.display = 'none';

                    alert('Profile image removed successfully!');

                    // Refresh the page to update the user card
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    alert('Error: ' + (data.message || 'Failed to remove profile image'));
                }
            })
            .catch(error => {
                removeBtn.classList.remove('loading');
                removeBtn.disabled = false;
                console.error('Error:', error);
                alert('An error occurred while removing the profile image');
            });
        }

        function deleteUser() {
            // Get current user's username for confirmation
            const username = document.getElementById('modalUsername').textContent;
            document.getElementById('deleteUsername').textContent = username;
            document.getElementById('deleteAdminPassword').value = '';

            // Show delete confirmation modal
            document.getElementById('deleteModal').style.display = 'block';
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
            document.getElementById('deleteAdminPassword').value = '';
        }

        function confirmDeleteUser() {
            const adminPassword = document.getElementById('deleteAdminPassword').value.trim();

            if (!adminPassword) {
                alert('Please enter admin password');
                return;
            }

            // Show loading state
            const confirmBtn = document.querySelector('#deleteModal .btn-delete');
            confirmBtn.classList.add('loading');
            confirmBtn.disabled = true;

            // Send AJAX request to delete user
            fetch('/delete_user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: currentUserId,
                    admin_password: adminPassword
                })
            })
            .then(response => response.json())
            .then(data => {
                confirmBtn.classList.remove('loading');
                confirmBtn.disabled = false;

                if (data.success) {
                    alert('Success: ' + data.message);
                    closeDeleteModal();
                    closeUserModal();
                    // Refresh the page to update the user list
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                confirmBtn.classList.remove('loading');
                confirmBtn.disabled = false;
                console.error('Error:', error);
                alert('An error occurred while deleting the user');
            });
        }

        function closeUserModal() {
            document.getElementById('userModal').style.display = 'none';
            setViewMode(); // Reset to view mode when closing
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const userModal = document.getElementById('userModal');
            const deleteModal = document.getElementById('deleteModal');

            if (event.target == userModal) {
                closeUserModal();
            } else if (event.target == deleteModal) {
                closeDeleteModal();
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const deleteModal = document.getElementById('deleteModal');
                const userModal = document.getElementById('userModal');

                if (deleteModal.style.display === 'block') {
                    closeDeleteModal();
                } else if (userModal.style.display === 'block') {
                    closeUserModal();
                }
            }
        });
    </script>

 <p style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; bottom: 0px;  margin-bottom: 15px; width: 100%;">© URLCHECK 2025.All rights reserved</p>
</body>
</html>
{% endblock %}
