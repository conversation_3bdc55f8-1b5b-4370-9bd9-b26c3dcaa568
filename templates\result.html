{% if is_admin %}
    {% extends "header_admin.html" %}
{% else %}
    {% extends "header.html" %}
{% endif %}

{% block content %}
<!DOCTYPE html>
<html>
<head>
    <title>Scan Result</title>
    <style>
       /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark mode base styles */
body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}

/* Input field styling for dark mode */
input[type="text"],
input[type="text"]:focus,
input[type="text"]:active,
input[type="text"]:hover,
input[type="password"],
input[type="password"]:focus,
input[type="email"],
input[type="email"]:focus,
textarea,
textarea:focus,
select,
select:focus {
    background-color: #333333 !important;
    color: #ffffff !important;
    border: 1px solid #444444;
    outline-color: #1e3c72;
}

/* Override browser autofill styles which often use white backgrounds */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
    -webkit-text-fill-color: #ffffff !important;
    transition: background-color 5000s ease-in-out 0s;
}

        /* Main content container */

        /* Main container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
            margin-bottom: 100px;
        }

        /* Page title */
        .page-title {
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 30px;
            text-align: center;
            letter-spacing: -0.04em;

        }



        .content p {
            color: #b3b3b3;
            font-size: 14px;
            margin-bottom: 30px;
            text-align: center;

        }



        /* URL display */
        .url-display {
            background-color: #282828;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            width: 68%;
            display: block;
            margin-left: auto;
            margin-right: auto;

        }

        .url-display input[type="text"] {
            width: 100%;
            padding: 12px;
            font-size: 15px;
            border: none;
            background-color: #333333;
            color: #ffffff;
            border-radius: 4px;
            box-sizing: border-box;
            text-align: center;
        }

        /* Result section */
        .result-section {
            margin-bottom: 25px;
            width: 33%;
            margin-left: auto;
            margin-right: auto;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .result-label {
            font-size: 30px;
            color: #b3b3b3;
            margin-bottom: 0px;
        }

        /* Badge styling */
        .badge {
            display: block;
            padding: 8px 16px;
            border-radius: 5px;
            font-weight: 700;
            font-size: 16px;
            text-align: center;
            width: 200%;
            min-width: 90px;
            position: relative;
            transform: translateX(0);
        }

        .safe-badge {
            background-color: #1DB954; /* Spotify green */
            color: #000000;
        }

        .suspicious-badge {
            background-color: #E91429; /* Spotify red */
            color: #000000;
        }

        /* Note styling */
        .note {
            background-color: #282828;
            font-size: 14px;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #5dcef0; /* Spotify green */
            text-align: left;
            margin-bottom: 25px;
            width: 66%;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        .note strong {
            color: #ffffff;
        }

        /* Button group */
        .button-group {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin: 0 auto 40px auto;
            padding: 20px 0;
            width: 100%;
        }

        .button {
            padding: 14px;
            border-radius: 30px;
            font-weight: 700;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .primary-button {
            background-color: #121212;
            color: #5dcef0;
            border: none;
            padding: 14px 28px;
            text-align: center;
            width: 200px;
            height: 52px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            font-weight: 600;
            font-size: 15px;
            transition: all 0.3s ease;
        }

        .primary-button:hover {
            background-color: #121212;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(74, 164, 192, 0.4);
        }

        .secondary-button {
            background-color: #4aa4c0;
            color: #000000;
            border: none;
            padding: 14px 28px;
            text-align: center;
            width: 200px;
            height: 52px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            font-weight: 600;
            font-size: 15px;
            box-shadow: 0 4px 12px rgba(74, 164, 192, 0.3);
            transition: all 0.3s ease;
        }

        .secondary-button:hover {
            background-color: #5dcef0;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(74, 164, 192, 0.4);
        }

        .login-note {
            margin-top: 20px;
            margin-bottom: px;
            font-style: italic;
            color: #b3b3b3;
        }

        /* Standardized Mobile Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 0 20px;
                margin-bottom: 120px;
            }

            .page-title {
                font-size: 32px;
                margin-bottom: 30px;
                margin-top: 50px;
            }

            .badge {
                font-size: 14px;
                padding: 6px 12px;
            }

            .button {
                padding: 12px;
                font-size: 15px;
            }

            .url-display, .result-section, .note {
                width: 100%;
                margin-left: 0;
                margin-right: 0;
            }

            .button-group {
                flex-direction: column;
                gap: 15px;
                margin: 25px auto 30px auto;
                padding: 15px 0;
            }

            .primary-button, .secondary-button {
                width: 100%;
                max-width: 280px;
                height: 48px;
                font-size: 14px;
            }
        }

        @media (max-width: 600px) {
            .container {
                margin: 15px auto;
                padding: 0 15px;
                margin-bottom: 100px;
            }

            .page-title {
                font-size: 28px;
                margin-bottom: 25px;
            }

            .badge {
                font-size: 13px;
                padding: 5px 10px;
            }

            .button {
                padding: 10px;
                font-size: 14px;
            }

            .url-display, .result-section, .note {
                width: 100%;
            }

            .button-group {
                gap: 12px;
                margin: 20px auto 25px auto;
                padding: 12px 0;
            }

            .primary-button, .secondary-button {
                max-width: 260px;
                height: 44px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px auto;
                padding: 0 10px;
                margin-bottom: 80px;
            }

            .page-title {
                font-size: 24px;
                margin-bottom: 20px;
            }

            .badge {
                font-size: 12px;
                padding: 4px 8px;
            }

            .button {
                padding: 8px;
                font-size: 13px;
            }

            .url-display, .result-section, .note {
                width: 100%;
                padding: 8px;
            }

            .button-group {
                gap: 10px;
                margin: 15px auto 20px auto;
                padding: 10px 0;
            }

            .primary-button, .secondary-button {
                max-width: 240px;
                height: 40px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">Scan Result</h1>


        <div class="url-display">
            <input type="text" value="{{ url }}" readonly>
        </div>

        <div class="result-section">
            <p style="text-align:center; font-size: 16px; color: #c7c7c7; bottom: 0px;  margin-bottom: 7px;">Result:</p>
            <span class="badge {% if prediction == 'Safe' %}safe-badge{% else %}suspicious-badge{% endif %}">
                {{ prediction }}
            </span>
        </div>

        <div class="note">
            <strong>Note:</strong> {{ note }}
        </div>

        <div class="button-group">
            <a href="/" class="button primary-button">Scan Another URL</a>

           {% if session and session.get('email') and session.get('email') != 'guest' and session.get('email') != 'none' %}
<form method="post" action="/details" style="margin: 0;">
    <input type="hidden" name="url" value="{{ url }}">
    <button type="submit" class="button secondary-button">View Detailed Analysis</button>
</form>
{% else %}
<p class="login-note">Login to view detailed analysis</p>
{% endif %}
        </div>
    </div>

    <p2 style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; position: fixed; bottom: 0px;  margin-bottom: 15px; width: 100%; left: 0px;">© URLCHECK 2025.All rights reserved</p2>
</body>
</html>
{% endblock %}