{% extends "header.html" %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile</title>
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

        }

        body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}

        /* Main container */
        .content {
            max-width: 800px;
            margin: 40px auto;
            padding: 0 20px;
        }



        /* Page title */
        .page-title {
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 20px;
            text-align: center;
            letter-spacing: -0.04em;
        }

        /* Profile container */
        .profile-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
            padding: 32px;
            background-color: #12121200;
            border-radius: 8px;
        }

        /* Image container */
        .image-container {
            margin-bottom: 24px;
            position: relative;
        }

        /* Profile picture */
        .profile-picture {
            border-radius: 50%;
            width: 160px;
            height: 160px;
            object-fit: cover;
            border: 4px solid #5dcef0;
            box-shadow: 0 8px 24px rgba(0,0,0,0.5);
        }

        /* File input styling */
        #profile_picture {
            background-color: transparent;
            color: #b3b3b3;
            border: 1px solid #333333;
            border-radius: 500px;
            padding: 8px 20px;
            margin-top: 0px;
            font-size: 8px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;

        }

        #profile_picture::-webkit-file-upload-button {
            background-color: #4aa4c0;
            color: #000000;
            border: none;
            border-radius: 500px;
            padding: 8px 24px;
            margin-right: 16px;
            font-weight: 700;
            cursor: pointer;
            text-transform: uppercase;

            font-size: 8px;
        }

        #profile_picture:hover {
            border-color: #5dcef0;
            transform: scale(1.04);
        }

        /* Form section */
        .form-section {
            background-color: #333333;
            border-radius: 8px;
            padding: 10px 30px;
            margin-bottom: 24px;
        }

        .form-section h2 {
            font-size: 24px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 24px;
            border-bottom: 1px solid #282828;
            padding-bottom: 16px;
        }

        /* Form group styling */
        .form-group {
            margin-bottom: 2px;
        }

        .form-group.row {
            display: flex;
            align-items: center;
            gap: 24px;
            padding: 16px 0;
            border-bottom: 1px solid #282828;
        }

        .form-group.row:last-child {
            border-bottom: none;
        }

        /* Form label */
        .form-label {
            min-width: 140px;
            font-weight: 400;
            color: #b3b3b3;
            font-size: 16px;
        }

        /* Form info display (read-only fields) */
        .form-info {
            flex: 1;
            font-size: 16px;
            color: #ffffff;
            font-weight: 400;
        }

        .form-info.readonly {
            color: #b3b3b3;
            font-style: italic;
        }

        /* Form input */
        .form-input {
            flex: 1;
            padding: 12px 16px;
            font-size: 16px;
            border: 1px solid #333333;
            border-radius: 4px;
            background-color: #121212;
            color: #ffffff;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #5dcef0;
            box-shadow: 0 0 0 3px rgba(29, 185, 84, 0.1);
        }

        .form-input:disabled {
            background-color: #282828;
            color: #333333;
            cursor: not-allowed;
        }

        /* Edit button */
        .edit-button {
             margin-bottom: 2px;
            background-color: transparent;
            color: #5dcef0;
            border: 1px solid #5dcef0;
            border-radius: 500px;
            padding: 8px 24px;
            font-size: 12px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1.76px;
            min-width: 80px;
        }

        .edit-button:hover {
            background-color: #5dcef0;
            color: #000000;
            transform: scale(1.04);
        }

        /* Form actions */
        .form-actions {
            display: flex;
            justify-content: center;
            margin-top: 28px;
        }

        /* Save button */
        .save-button {
            background-color: #4aa4c0;
            color: #000000;
            border: none;
            border-radius: 500px;
            padding: 16px 48px;
            font-size: 14px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1.76px;
        }

        .save-button:hover {
            background-color: #5dcef0;
            transform: scale(1.04);
        }

        .save-button:disabled {
            background-color: #333333;
            color: #b3b3b3;
            cursor: not-allowed;
            transform: none;
        }

        /* Flash messages */
        .flash-messages {
            position: fixed;
            top: 85px;
            right: -2px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }
       .flash-messages li {
            background-color: #00b2f880; /* Spotify red */
            color: white;
            padding: 15px 25px;
            margin-top: 10px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            font-size: 14px;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 5s forwards;
            cursor: pointer;
            min-width: 250px;
        }

        @keyframes slideInOut {
            0% { opacity: 0; transform: translateX(100%); }
            10% { opacity: 1; transform: translateX(0); }
            90% { opacity: 1; transform: translateX(0); }
            100% { opacity: 0; transform: translateX(100%); }
        }

        /* Password Modal Styles */
        .modal-overlay {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: #282828;
            padding: 30px;
            border-radius: 12px;
            max-width: 450px;
            width: 90%;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
            border: 1px solid #333;
        }

        .modal-content h2 {
            color: #ffffff;
            margin-bottom: 25px;
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            font-family: 'Roboto', sans-serif;
        }

        .modal-form-group {
            margin-bottom: 20px;
        }

        .modal-form-group label {
            display: block;
            color: #ffffff;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .modal-form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #333;
            background-color: #1a1a1a;
            color: #ffffff;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .modal-form-group input:focus {
            border-color: #5dcef0;
            box-shadow: 0 0 0 2px rgba(93, 206, 240, 0.15);
            outline: none;
        }

        /* Tooltip styles for modal */
        .modal-tooltip {
            position: relative;
            display: block;
            width: 100%;
        }

        .modal-tooltip input {
            width: 100%;
            box-sizing: border-box;
        }

        .modal-tooltip .tooltiptext {
            visibility: hidden;
            width: 280px;
            background-color: #333333;
            color: #ffffff;
            text-align: left;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1001;
            bottom: 100%;
            left: 60px;
            margin-bottom: 10px;
            opacity: 0;
            transition: opacity 0.3s;
            border: 1px solid #5dcef0;
            font-size: 12px;
            line-height: 1.4;
        }

        .modal-tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 20px;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #5dcef0 transparent transparent transparent;
        }

        .modal-tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .tooltip-criteria {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .tooltip-criteria li {
            margin: 5px 0;
            color: #cb1023;
            font-size: 11px;
        }

        .tooltip-criteria li.valid {
            color: #1DB954;
        }

        .tooltip-criteria li::before {
            content: "✗ ";
            margin-right: 5px;
        }

        .tooltip-criteria li.valid::before {
            content: "✓ ";
        }

         .forgot-link {
            margin-top: -19px;
            margin-bottom: 18px;
            text-align: right;
        }

        .forgot-link a {
            text-decoration: none;
            color: #b3b3b3;
            font-size: 12px;
            transition: color 0.3s ease;
        }

        .forgot-link a:hover {
            color: #4aa4c0;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
        }

        .modal-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            min-width: 100px;
        }

        .btn-update {
            background-color: rgb(74, 164, 192);
            color: #000000;
        }

        .btn-update:hover:enabled {
            background-color: #5dcef0;
            transform: scale(1.02);
        }

        .btn-update:disabled {
            background-color: #333333;
            color: #666666;
            cursor: not-allowed;
        }

        .btn-cancel {
            background-color: transparent;
            color: #b3b3b3;
            border: 1px solid #666666;
        }

        .btn-cancel:hover {
            background-color: #666666;
            color: #ffffff;
        }

        /* Modal Flash Messages */
        .modal-flash-messages {
            margin-bottom: 20px;
            border-radius: 6px;
            padding: 12px;
            font-size: 14px;
            line-height: 1.4;
        }

        .modal-flash-messages.error {
            background-color: rgba(233, 20, 41, 0.2);
            border: 1px solid #ff0019a8;
            color: #ffffff;
        }

        .modal-flash-messages.success {
            background-color: rgba(29, 185, 84, 0.2);
            border: 1px solid #1DB954;
            color: #ffffff;
        }

        .modal-flash-content {
            margin: 0;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .content {
                padding: 24px 16px;
            }

            .page-title {
                font-size: 32px;
                margin-bottom: 32px;
            }

            .profile-picture {
                width: 150px;
                height: 150px;
            }

            .form-section {
                padding: 24px 16px;
            }

            .form-group.row {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
                padding: 16px 0;
            }

            .form-label {
                min-width: auto;
                margin-bottom: 8px;
            }

            .form-input {
                width: 100%;
            }

            .edit-button {
                align-self: flex-start;
                margin-top: 8px;
            }

            .flash-messages {
                right: 16px;
                left: 16px;
            }

            .flash-messages li {
                min-width: auto;
            }

            .modal-content {
                margin: 20px;
                width: calc(100% - 40px);
                max-width: none;
            }

            .modal-tooltip .tooltiptext {
                width: 250px;
                left: 20px;
            }

            .modal-buttons {
                flex-direction: column;
                gap: 10px;
            }

            .modal-btn {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .content {
                padding: 16px 12px;
            }

            .page-title {
                font-size: 28px;
            }

            .profile-container {
                padding: 24px 16px;
            }

            .profile-picture {
                width: 120px;
                height: 120px;
            }

            .form-section {
                padding: 20px 12px;
            }

            .save-button {
                padding: 14px 32px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
  <div class="content">
    <h1 class="page-title">Profile</h1>

    <form action="{{ url_for('profile') }}" method="POST" enctype="multipart/form-data">
      <!-- Profile Picture Section -->
      <div class="profile-container">
        <div class="image-container">
          {% if profile_picture %}
            <img src="data:image/jpeg;base64,{{ profile_picture | b64encode }}" class="profile-picture" alt="Profile Picture">
          {% else %}
            <img src="{{ url_for('static', filename='profilee.jpg') }}" class="profile-picture" alt="Default Profile Picture">
          {% endif %}
        </div>
        <input type="file" id="profile_picture" name="profile_picture" accept="image/*">
      </div>

      <!-- Flash Messages -->
      {% with messages = get_flashed_messages() %}
        {% if messages %}
          <ul class="flash-messages" id="flash-messages">
            {% for message in messages %}
              <li>{{ message }}</li>
            {% endfor %}
          </ul>
        {% endif %}
      {% endwith %}

      <!-- Personal Information Section -->
      <div class="form-section">
        <h2 style="margin-bottom: 0px; margin-top: 10px;">Personal Information</h2>


        <div class="form-group row">
          <label for="username" class="form-label">Username</label>
          <div class="form-info">
            <span id="username_display">{{ username }}</span>
            <input type="text" id="username_input" name="username" value="{{ username }}" class="form-input" style="display:none;" required>
          </div>
          <button type="button" class="edit-button" onclick="enableEdit('username')">Edit</button>
        </div>

        <div class="form-group row">
          <label for="email" class="form-label">Email</label>
          <div class="form-info">{{ email }}</div>
        </div>

        <div class="form-group row">
          <label for="password" class="form-label">Password</label>
          <div class="form-info">••••••••••</div>
          <button type="button" class="edit-button" onclick="enableEdit('password')">Edit</button>
        </div>
      </div>

      <!-- Account Information Section -->
      <div class="form-section">
        <h2 style="margin-bottom: 0px; margin-top: 10px;">Account Information</h2>

        <div class="form-group row">
          <label class="form-label">Account Type</label>
          <div class="form-info readonly">Business Plan</div>
        </div>

        <div class="form-group row">
          <label class="form-label">Token Number</label>
          <div class="form-info readonly">{{ token_number }}</div>
        </div>

        <div class="form-group row">
          <label class="form-label">Start Date</label>
          <div class="form-info readonly">{{ start_date }}</div>
        </div>

        <div class="form-group row">
          <label class="form-label">Expiry Date</label>
          <div class="form-info readonly">{{ expiry_date }}</div>
        </div>
      </div>

      <div class="form-actions">
        <button type="submit" class="save-button">Save Changes</button>
      </div>
    </form>
  </div>

  <!-- Password Change Modal -->
  <div class="modal-overlay" id="passwordModal">
    <div class="modal-content">
      <h2>Change Password</h2>

      <!-- Modal Flash Messages -->
      <div id="modal-flash-messages" class="modal-flash-messages" style="display: none;">
        <div id="modal-flash-content" class="modal-flash-content"></div>
      </div>

      <form id="passwordForm" method="POST" action="{{ url_for('profile') }}">
        <input type="hidden" name="action" value="change_password">

        <div class="modal-form-group">
          <label for="current_password">Current Password:</label>
          <input type="password" id="current_password" name="current_password" required>
        </div>

        <div class="modal-form-group">
          <label for="new_password">New Password:</label>
          <div class="modal-tooltip">
            <input type="password" id="new_password" name="new_password" oninput="validateModalPassword()" required>
            <span class="tooltiptext">
              <strong>Password Requirements:</strong>
              <ul class="tooltip-criteria">
                <li id="modal-uppercase">At least 1 uppercase letter (A-Z)</li>
                <li id="modal-lowercase">At least 1 lowercase letter (a-z)</li>
                <li id="modal-digit">At least 1 digit (0-9)</li>
                <li id="modal-symbol">At least 1 symbol (!@#$%^&*)</li>
                <li id="modal-length">At least 8 characters</li>
              </ul>
            </span>
          </div>
        </div>
        <div class="forgot-link">
                <a href="/confirmation">Forgot Password?</a>
            </div>

        <div class="modal-buttons">
          <button type="button" class="modal-btn btn-cancel" onclick="closePasswordModal()">Cancel</button>
          <button type="submit" class="modal-btn btn-update" id="updatePasswordBtn" disabled>Update Password</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    function enableEdit(fieldName) {
      if (fieldName === 'password') {
        // Show password modal instead of inline editing
        showPasswordModal();
        return;
      }

      const display = document.getElementById(fieldName + "_display");
      const input = document.getElementById(fieldName + "_input");
      const button = event.target;

      if (display && input) {
        display.style.display = "none";
        input.style.display = "block";
        input.focus();

        // Change button text to "Cancel" and add cancel functionality
        button.textContent = "Cancel";
        button.onclick = function() {
          cancelEdit(fieldName);
        };
      }
    }

    function cancelEdit(fieldName) {
      const display = document.getElementById(fieldName + "_display");
      const input = document.getElementById(fieldName + "_input");
      const button = event.target;

      if (display && input) {
        display.style.display = "block";
        input.style.display = "none";

        // Reset input value to original
        if (fieldName === 'username') {
          input.value = "{{ username }}";
        } else if (fieldName === 'email') {
          input.value = "{{ email or '' }}";
        } else if (fieldName === 'password') {
          input.value = "";
        }

        // Change button back to "Edit"
        button.textContent = "Edit";
        button.onclick = function() {
          enableEdit(fieldName);
        };
      }
    }

    // Password Modal Functions
    function showPasswordModal() {
      document.getElementById('passwordModal').style.display = 'flex';
      document.getElementById('current_password').focus();
    }

    function closePasswordModal() {
      document.getElementById('passwordModal').style.display = 'none';
      // Reset form
      document.getElementById('passwordForm').reset();
      // Reset validation
      resetPasswordValidation();
      // Hide flash messages
      hideModalFlash();
    }

    function showModalFlash(message, type) {
      const flashContainer = document.getElementById('modal-flash-messages');
      const flashContent = document.getElementById('modal-flash-content');

      flashContent.textContent = message;
      flashContainer.className = 'modal-flash-messages ' + type;
      flashContainer.style.display = 'block';

      // Auto-hide after 5 seconds
      setTimeout(hideModalFlash, 5000);
    }

    function hideModalFlash() {
      const flashContainer = document.getElementById('modal-flash-messages');
      flashContainer.style.display = 'none';
      flashContainer.className = 'modal-flash-messages';
    }

    function validateModalPassword() {
      const password = document.getElementById("new_password").value;
      const updateBtn = document.getElementById("updatePasswordBtn");

      // Regular Expressions for Password
      const hasUpper = /[A-Z]/.test(password);
      const hasLower = /[a-z]/.test(password);
      const hasDigit = /[0-9]/.test(password);
      const hasSymbol = /[!@#$%^&*]/.test(password);
      const isLongEnough = password.length >= 8;

      // Toggle valid/invalid classes for Password tooltip
      document.getElementById("modal-uppercase").classList.toggle("valid", hasUpper);
      document.getElementById("modal-lowercase").classList.toggle("valid", hasLower);
      document.getElementById("modal-digit").classList.toggle("valid", hasDigit);
      document.getElementById("modal-symbol").classList.toggle("valid", hasSymbol);
      document.getElementById("modal-length").classList.toggle("valid", isLongEnough);

      // Enable/disable update button based on validation
      const allValid = hasUpper && hasLower && hasDigit && hasSymbol && isLongEnough;
      updateBtn.disabled = !allValid;
    }

    function resetPasswordValidation() {
      const criteria = ['modal-uppercase', 'modal-lowercase', 'modal-digit', 'modal-symbol', 'modal-length'];
      criteria.forEach(id => {
        document.getElementById(id).classList.remove('valid');
      });
      document.getElementById("updatePasswordBtn").disabled = true;
    }

    // Auto-hide flash messages after animation
    document.addEventListener('DOMContentLoaded', function() {
      const flashMessages = document.getElementById('flash-messages');
      if (flashMessages) {
        setTimeout(function() {
          flashMessages.style.display = 'none';
        }, 5000);
      }

      // Close modal when clicking outside of it
      const modal = document.getElementById('passwordModal');
      modal.addEventListener('click', function(event) {
        if (event.target === modal) {
          closePasswordModal();
        }
      });

      // Prevent closing when clicking inside modal content
      const modalContent = modal.querySelector('.modal-content');
      modalContent.addEventListener('click', function(event) {
        event.stopPropagation();
      });

      // Handle password form submission with AJAX
      const passwordForm = document.getElementById('passwordForm');
      passwordForm.addEventListener('submit', function(event) {
        event.preventDefault();

        const formData = new FormData(passwordForm);
        const updateBtn = document.getElementById('updatePasswordBtn');

        // Disable button and show loading state
        updateBtn.disabled = true;
        updateBtn.textContent = 'Updating...';

        fetch('{{ url_for("profile") }}', {
          method: 'POST',
          body: formData
        })
        .then(response => response.json())
        .catch(() => {
          // If JSON parsing fails, treat as error
          return { success: false, message: 'An error occurred while updating password.' };
        })
        .then(data => {
          if (data.success) {
            showModalFlash(data.message, 'success');
            // Close modal after 2 seconds on success
            setTimeout(() => {
              closePasswordModal();
              // Reload page to refresh any session data
              window.location.reload();
            }, 2000);
          } else {
            showModalFlash(data.message, 'error');
          }
        })
        .catch(error => {
          showModalFlash('An error occurred while updating password.', 'error');
        })
        .finally(() => {
          // Re-enable button
          updateBtn.disabled = false;
          updateBtn.textContent = 'Update Password';
          // Re-validate to set correct disabled state
          validateModalPassword();
        });
      });
    });
  </script>
</body>
</html>
{% endblock %}