# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Environment files
.env.example

# Documentation
README.md
*.md

# Backup files
*.bak
backup_models/

# Test files
test_*.py
*_test.py

# Temporary files
*.tmp
*.temp
