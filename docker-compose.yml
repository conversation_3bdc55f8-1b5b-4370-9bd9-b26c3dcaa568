version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: urlscanner_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword123}
      MYSQL_DATABASE: ${DB_NAME:-urlscanner}
      MYSQL_USER: ${DB_USER:-user1}
      MYSQL_PASSWORD: ${DB_PASSWORD:-newpassword123}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - urlscanner_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-rootpassword123}"]
      timeout: 20s
      retries: 10
      interval: 10s
      start_period: 40s

  # Flask Application
  web:
    build: .
    container_name: urlscanner_web
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - DB_HOST=mysql
      - DB_USER=${DB_USER:-user1}
      - DB_PASSWORD=${DB_PASSWORD:-newpassword123}
      - DB_NAME=${DB_NAME:-urlscanner}
      - FLASK_ENV=production
      - FLASK_APP=app.py
    volumes:
      - ./static/uploads:/app/static/uploads
      - ./logs:/app/logs
      - ./training_model:/app/training_model
      - ./features_extraction:/app/features_extraction
      - ./dataset:/app/dataset
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - urlscanner_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      timeout: 10s
      retries: 5
      interval: 30s
      start_period: 60s

  # phpMyAdmin (Optional - for database management)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: urlscanner_phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: ${DB_USER:-user1}
      PMA_PASSWORD: ${DB_PASSWORD:-newpassword123}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword123}
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - urlscanner_network

volumes:
  mysql_data:
    driver: local

networks:
  urlscanner_network:
    driver: bridge
