{% extends "header.html" %}
{% block content %}

<!DOCTYPE html>
<html>
<head>
    <title>Contact Us</title>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark mode base styles */
        body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}

        /* Input field styling for dark mode */
        input[type="text"],
        input[type="text"]:focus,
        input[type="text"]:active,
        input[type="text"]:hover,
        input[type="password"],
        input[type="password"]:focus,
        input[type="email"],
        input[type="email"]:focus,
        textarea,
        textarea:focus,
        select,
        select:focus {
            background-color: #333333 !important;
            color: #ffffff !important;
            border: 1px solid #444444;
            outline-color: #1e3c72;
        }

        /* Override browser autofill styles */
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
            -webkit-text-fill-color: #ffffff !important;
            transition: background-color 5000s ease-in-out 0s;
        }

        /* Main container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
            margin-bottom: 95px;
        }

        /* Page title */
        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 30px;
            text-align: center;
            letter-spacing: -0.04em;
        }

        .content p {
            color: #b3b3b3;
            font-size: 14px;
            margin-bottom: 30px;
            text-align: center;
        }

        /* Two-column layout container */
        .contact-layout {
            display: flex;
            gap: 60px;
            max-width: 1000px;
            margin: 0 auto;
            align-items: flex-start;
        }

        /* Information section (left side) */
        .information-section {
            flex: 1;
            min-width: 300px;
        }

        .information-section h2 {
            font-size: 24px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 30px;
            letter-spacing: -0.02em;
        }

        .info-item {
            margin-bottom: 35px;
        }

        .info-item-content {
            display: flex;
            align-items: flex-start;
            gap: 16px;
        }

        .info-item .icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #5dcef0, #4aa4c0);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            color: #000000;
            font-weight: bold;
            box-shadow: 0 4px 12px rgba(93, 206, 240, 0.3);
            flex-shrink: 0;
            margin-top: 2px;
        }

        .info-text {
            flex: 1;
        }

        .info-text h3 {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .info-text p {
            color: #e0e0e0;
            font-size: 15px;
            line-height: 1.6;
            margin: 0;
            font-weight: 400;
        }

        .info-item a {
            color: #5dcef0;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .info-item a:hover {
            color: #4aa4c0;
        }

        /* Contact Form Styles */
        .contact-form {
            display: flex;
            flex-direction: column;
            flex: 1;
            min-width: 400px;
        }

        .contact-form label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #b3b3b3;
            font-size: 12px;
        }

        .contact-form input[type="text"],
        .contact-form input[type="email"],
        .contact-form textarea {
            padding: 10px 12px;
            border: 1px solid #333;
            background-color: #282828;
            border-radius: 30px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #ffffff;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .contact-form textarea {
            border-radius: 15px;
            min-height: 120px;
            resize: vertical;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 15px;
        }

        .contact-form input:focus,
        .contact-form textarea:focus {
            border-color: #5dcef0;
            box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.15);
            outline: none;
        }

        .contact-form input::placeholder,
        .contact-form textarea::placeholder {
            color: #888;
        }

        .contact-form button {
            background-color: #4aa4c0;
            color: rgb(0, 0, 0);
            border: none;
            border-radius: 30px;
            padding: 14px 0;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 10px;
        }

        .contact-form button:disabled {
            background-color: #333333;
            cursor: not-allowed;
        }

        .contact-form button:hover:enabled {
            background-color: #5dcef0;
            transform: scale(1.02);
        }

        /* Flash messages container */
        .flash-messages {
            position: fixed;
            top: 85px;
            right: -2px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .flash-messages li {
            background-color: #ff0019a8;
            color: white;
            padding: 15px 25px;
            margin-top: 10px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            font-size: 14px;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 5s forwards;
            cursor: pointer;
            min-width: 250px;
        }

        .flash-messages li.success {
            background-color: rgba(0, 178, 248, 0.502);
        }

        .flash-messages li.error {
            background-color: #ff0019a8;
        }

        /* Animation for slide in from right and fade out */
        @keyframes slideInOut {
            0% {
                opacity: 0;
                transform: translateX(100%);
            }
            10% {
                opacity: 1;
                transform: translateX(0);
            }
            90% {
                opacity: 1;
                transform: translateX(0);
            }
            100% {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        @keyframes slideOut {
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        /* Additional link section */
        .contact-link {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
        }

        .contact-link a {
            color: #b3b3b3;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .contact-link a:hover {
            color: #4aa4c0;
        }

        /* reCAPTCHA styling */
        .recaptcha-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            transform: scale(0.9);
            transform-origin: center;
        }

        /* Dark theme override for reCAPTCHA */
        .g-recaptcha {
            filter: invert(1) hue-rotate(180deg);
            border-radius: 4px;
        }

        /* Standardized Mobile Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 0 20px;
                margin-bottom: 90px;
            }

            .page-title {
                font-size: 32px;
                margin-bottom: 30px;
                margin-top: 50px;
            }

            .contact-layout {
                flex-direction: column;
                gap: 40px;
                max-width: 100%;
            }

            .information-section {
                min-width: auto;
                padding: 24px 20px;
                margin-bottom: 20px;
            }

            .information-section h2 {
                font-size: 24px;
                margin-bottom: 24px;
            }

            .information-section h2::after {
                width: 50px;
                height: 2px;
            }

            .info-item {
                margin-bottom: 20px;
                padding: 16px;
            }

            .info-item-content {
                gap: 12px;
            }

            .info-item .icon {
                width: 22px;
                height: 22px;
                font-size: 12px;
            }

            .info-text h3 {
                font-size: 16px;
                margin-bottom: 6px;
            }

            .info-text p {
                font-size: 14px;
            }

            .contact-form {
                min-width: auto;
                width: 100%;
            }

            .contact-form button {
                padding: 14px;
                font-size: 16px;
            }

            .flash-messages li {
                min-width: 250px;
                padding: 15px 20px;
                font-size: 14px;
            }

            .recaptcha-container {
                transform: scale(0.8);
            }
        }

        @media (max-width: 600px) {
            .container {
                margin: 15px auto;
                padding: 0 15px;
                margin-bottom: 100px;
            }

            .page-title {
                font-size: 28px;
                margin-bottom: 25px;
            }

            .contact-layout {
                gap: 30px;
            }

            .information-section {
                padding: 20px 16px;
                margin-bottom: 16px;
            }

            .information-section h2 {
                font-size: 22px;
                margin-bottom: 20px;
            }

            .info-item {
                margin-bottom: 16px;
                padding: 14px;
            }

            .info-item-content {
                gap: 10px;
            }

            .info-item .icon {
                width: 20px;
                height: 20px;
                font-size: 11px;
            }

            .info-text h3 {
                font-size: 15px;
                margin-bottom: 5px;
            }

            .info-text p {
                font-size: 13px;
            }

            .contact-form {
                width: 100%;
            }

            .contact-form button {
                padding: 12px;
                font-size: 15px;
            }

            .flash-messages li {
                min-width: 200px;
                padding: 12px 20px;
                font-size: 13px;
            }

            .recaptcha-container {
                transform: scale(0.75);
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px auto;
                padding: 0 10px;
                margin-bottom: 80px;
            }

            .page-title {
                font-size: 24px;
                margin-bottom: 20px;
            }

            .contact-layout {
                gap: 25px;
            }

            .information-section {
                padding: 16px 12px;
                margin-bottom: 12px;
            }

            .information-section h2 {
                font-size: 20px;
                margin-bottom: 16px;
            }

            .information-section h2::after {
                width: 40px;
                height: 2px;
            }

            .info-item {
                margin-bottom: 14px;
                padding: 12px;
            }

            .info-item-content {
                gap: 8px;
            }

            .info-item .icon {
                width: 18px;
                height: 18px;
                font-size: 10px;
            }

            .info-text h3 {
                font-size: 14px;
                margin-bottom: 4px;
            }

            .info-text p {
                font-size: 12px;
                line-height: 1.4;
            }

            .contact-form {
                width: 100%;
            }

            .contact-form button {
                padding: 10px;
                font-size: 14px;
            }

            .flash-messages li {
                min-width: 180px;
                padding: 10px 15px;
                font-size: 12px;
            }

            .recaptcha-container {
                transform: scale(0.7);
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="content">
        <h1 class="page-title">Contact Us</h1>
        <p>Have a question or feedback? Send us a message and we'll respond as soon as possible.</p>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
              <ul class="flash-messages" id="flash-messages">
                {% for category, message in messages %}
                  <li class="{{ category }}">{{ message }}</li>
                {% endfor %}
              </ul>
            {% endif %}
        {% endwith %}

        <div class="contact-layout">
            <!-- Information Section (Left Side) -->
            <div class="information-section">
                <h2>Information</h2>

                <div class="info-item">
                    <div class="info-item-content">
                        <span class="icon">📞</span>
                        <div class="info-text">
                            <h3>Call Us</h3>
                            <p>+601158719915 (syafiq)</p>
                        </div>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-item-content">
                        <span class="icon">✉️</span>
                        <div class="info-text">
                            <h3>Email</h3>
                            <p><a href="mailto:<EMAIL>?subject=Pertanyaan%20mengenai%20URLCheck.my&body=Salam,%0A%0ASaya ingin bertanya mengenai sistem URLCheck.my.%0A%0ATerima kasih." 
   id="contactUserBtn" 
   class="btn btn-primary">
   <EMAIL>
</a>
</p>
                        </div>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-item-content">
                        <span class="icon">🌐</span>
                        <div class="info-text">
                            <h3>Website</h3>
                            <p><a href="https://urlcheck.my/" target="_blank">https://urlcheck.my/</a></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Form (Right Side) -->
            <form method="post" class="contact-form" id="contact-form">
                <label for="name">Name:</label>
                <input type="text" name="name" id="name" placeholder="Enter your full name" required>

                <label for="email">Email:</label>
                <input type="email" name="email" id="email" placeholder="Enter your email address" required>

                <label for="subject">Subject:</label>
                <input type="text" name="subject" id="subject" placeholder="Enter the subject of your message" required>

                <label for="message">Your Message:</label>
                <textarea name="message" id="message" placeholder="Enter your message here..." required></textarea>

                <div class="recaptcha-container">
                    <div class="g-recaptcha" data-sitekey="{{ recaptcha_site_key }}"></div>
                </div>

                <button type="submit" id="submitBtn">SEND</button>
            </form>
        </div>

    </div>
</div>

<!-- Copyright notice -->

<script>
    // Remove flash message on click
    document.addEventListener('DOMContentLoaded', function() {
        const flashMessages = document.querySelectorAll('.flash-messages li');
        flashMessages.forEach(function(msg) {
            msg.addEventListener('click', function() {
                msg.style.animation = 'slideOut 0.5s forwards';
                setTimeout(() => {
                    msg.remove();
                }, 500);
            });
        });

        // Handle form submission
        const contactForm = document.getElementById('contact-form');
        const submitBtn = document.getElementById('submitBtn');

        contactForm.addEventListener('submit', function(e) {
            // Check if reCAPTCHA is completed
            const recaptchaResponse = grecaptcha.getResponse();
            if (recaptchaResponse.length === 0) {
                e.preventDefault();
                alert('Please complete the reCAPTCHA verification.');
                return false;
            }

            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = 'Sending...';
            }
        });
    });
</script>

</body>
</html>

{% endblock %}
