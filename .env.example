# Database Configuration
DB_HOST=mysql
DB_USER=user1
DB_PASSWORD=newpassword123
DB_NAME=urlscanner

# MySQL Root Password
MYSQL_ROOT_PASSWORD=rootpassword123

# Flask Configuration
FLASK_ENV=production
FLASK_APP=app.py
SECRET_KEY=your_secret_key_change_this_in_production

# Email Configuration (for OTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_USE_TLS=True

# reCAPTCHA Configuration
RECAPTCHA_SITE_KEY=your_recaptcha_site_key
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key

# Application Settings
MAX_CONTENT_LENGTH=524288000
UPLOAD_FOLDER=/app/static/uploads

# Security Settings
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax
