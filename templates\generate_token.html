{% extends "header_admin.html" %}

{% block content %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Generate Token</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark theme base styles */
        body {
            background: linear-gradient(135deg, #141719 0%, #121212 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        /* Input field styling for dark mode */
        input[type="text"],
        input[type="text"]:focus,
        input[type="text"]:active,
        input[type="text"]:hover,
        input[type="password"],
        input[type="password"]:focus,
        input[type="email"],
        input[type="email"]:focus,
        textarea,
        textarea:focus,
        select,
        select:focus {
            background-color: #333333 !important;
            color: #ffffff !important;
            border: 1px solid #444444;
            outline-color: #b148f3;
        }

        /* Override browser autofill styles */
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
            -webkit-text-fill-color: #ffffff !important;
            transition: background-color 5000s ease-in-out 0s;
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        /* Page title */
        .page-title {
            font-family: 'Roboto', sans-serif;
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 48px;
            text-align: center;
            letter-spacing: -0.04em;
        }

        /* Main content container */
        .content {
            width: 100%;
            background-color: transparent;
            margin: 0 auto;
            border-radius: 12px;
            padding: 20px 0;
            box-sizing: border-box;
        }


        /* Form styling */
        form label {
            display: block;
            font-weight: 600;
            margin-bottom: 10px;
            color: #b3b3b3;
            font-size: 16px;
        }

        form input[type="text"] {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            background-color: #282828;
            border: 1px solid #444444;
            border-radius: 8px;
            margin-bottom: 10px;
            box-sizing: border-box;
            color: #ffffff;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        form input[type="text"]:focus {
            border-color: #b148f3;
            box-shadow: 0 0 0 2px rgba(177, 72, 243, 0.2);
            outline: none;
        }

        /* Button styles */
        .btn {
            display: inline-block;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            border-radius: 30px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .btn-primary {
            background-color: #b148f3;
            color: #ffffff;
        }

        .btn-primary:hover {
            background-color: #9a3de0;
            transform: scale(1.05);
        }

        form input[type="submit"] {
            display: inline-block;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            border-radius: 30px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin-bottom: 20px;
            background-color: #b148f3;
            color: #ffffff;
        }

        form input[type="submit"]:hover {
            background-color: #9a3de0;
            transform: scale(1.05);
        }

        /* Flash messages - positioned next to button */
        .flash-messages {
            display: inline-block;
            margin-left: 20px;
            list-style: none;
            padding: 0;
            margin-top: 0;
            vertical-align: middle;
            position: relative;
            overflow: hidden;
        }

        .flash-messages li {
            color: #ffffff;
            font-size: 15px;
            margin: 0;
            padding: 0;
            background: none;
            border: none;
            box-shadow: none;
            white-space: nowrap;
            transform: translateX(-100%);
            animation: slideFromBehind 0.8s ease-out forwards;
        }

        .flash-messages li.success {
            color: #ffffff;
        }

        .flash-messages li.error {
            color: #ffffff;
        }

        /* Animation for sliding from behind button */
        @keyframes slideFromBehind {
            0% {
                transform: translateX(-100%);
                opacity: 0;
            }
            100% {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Button container for inline layout */
        .button-container {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
        }

        /* Responsive - make it more mobile friendly */
        @media (max-width: 600px) {
            .content {
                padding: 20px 15px;
                margin-top: 30px;
            }

            .page-title {
                font-size: 2rem;
            }

            .button-container {
                flex-direction: column;
                align-items: flex-start;
            }

            .flash-messages {
                margin-left: 0;
                margin-top: 10px;
            }

            form input[type="submit"] {
                padding: 12px;
                font-size: 15px;
            }
        }

        /* Table Styling - matching admin_archiveurl */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 14px;
        }

        th {
            background-color: #282828;
            color: #ffffff;
            font-weight: 600;
            text-align: left;
            padding: 15px;
            border-bottom: 2px solid #333333;
        }

        td {
            padding: 15px;
            border-bottom: 1px solid #333333;
            color: #b3b3b3;
            vertical-align: middle;
        }

        tr:hover {
            background-color: #222222;
        }

        tr:last-child td {
            border-bottom: none;
        }

        /* Center align specific columns */
        th:nth-child(1), td:nth-child(1),
        th:nth-child(5), td:nth-child(5),
        th:nth-child(6), td:nth-child(6) {
            text-align: center;
        }

        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 40px 0;
            color: #b3b3b3;
            font-style: italic;
        }

        /* Modal styles for confirmation popup */
        .modal-overlay {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: #282828;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            text-align: center;
            max-width: 500px;
            width: 90%;
            border: 1px solid #333333;
        }

        .modal-content h2 {
            color: #ffffff;
            margin-bottom: 15px;
            font-size: 24px;
            font-weight: 600;
        }

        .modal-content p {
            color: #b3b3b3;
            margin-bottom: 25px;
            font-size: 16px;
            line-height: 1.5;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .modal-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .btn-confirm {
            background-color: #b148f3;
            color: #ffffff;
        }

        .btn-confirm:hover {
            background-color: #9a3de0;
            transform: scale(1.05);
        }

        .btn-cancel {
            background-color: #333333;
            color: #ffffff;
        }

        .btn-cancel:hover {
            background-color: #444444;
            transform: scale(1.05);
        }

        /* Delete button styles */
        .delete-btn {
            background-color: #dc3545;
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .delete-btn:hover {
            background-color: #c82333;
            transform: scale(1.1);
        }

        .delete-icon {
            font-size: 16px;
        }

        /* Clickable token number styles */
        .token-number {
            color: #b3b3b3;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: normal;
        }

        .token-number:hover {
            color: #b148f3;
            text-decoration: underline;
            text-shadow: 0 0 8px rgba(177, 72, 243, 0.5);
            font-weight: 600;
        }

        /* Owner popup modal styles */
        .owner-modal-overlay {
            display: none;
            position: fixed;
            z-index: 10001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(5px);
        }

        .owner-modal-content {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            text-align: center;
            max-width: 450px;
            width: 90%;
            border: 1px solid rgba(177, 72, 243, 0.3);
            position: relative;
        }

        .owner-modal-content h2 {
            color: #b148f3;
            margin-bottom: 25px;
            font-size: 24px;
            font-weight: 600;
        }

        .owner-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .owner-info .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .owner-info .info-row:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }

        .owner-info .info-label {
            color: #b148f3;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .owner-info .info-value {
            color: #ffffff;
            font-size: 16px;
            font-weight: 500;
        }

        .no-owner-message {
            color: #b3b3b3;
            font-style: italic;
            font-size: 16px;
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .owner-modal-buttons {
            display: flex;
            justify-content: center;
        }

        .owner-back-btn {
            padding: 12px 30px;
            background: linear-gradient(135deg, #b148f3 0%, #9a3dd9 100%);
            color: #ffffff;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            min-width: 120px;
        }

        .owner-back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(177, 72, 243, 0.4);
        }

        /* Top controls container */
        .top-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            gap: 20px;
        }

        .generate-token-section {
            flex: 0 0 auto;
        }

        .search-section {
            flex: 0 0 auto;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Minimalist search input */
        .search-input {
            width: 250px;
            padding: 8px 12px;
            border: 1px solid #333333;
            border-radius: 6px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #b148f3;
            box-shadow: 0 0 0 1px rgba(177, 72, 243, 0.3);
        }

        .search-input::placeholder {
            color: #666666;
            font-style: italic;
        }

        .search-clear-btn {
            background: none;
            border: none;
            color: #666666;
            cursor: pointer;
            font-size: 14px;
            padding: 4px 6px;
            border-radius: 3px;
            transition: all 0.3s ease;
            display: none;
            margin-left: -25px;
            position: relative;
            z-index: 1;
        }

        .search-clear-btn:hover {
            color: #b148f3;
            background-color: rgba(177, 72, 243, 0.1);
        }

        .search-clear-btn.show {
            display: block;
        }

        .search-results-info {
            background-color: #1a1a1a;
            border-radius: 8px;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-left: 4px solid #b148f3;
            color: #b3b3b3;
            font-size: 14px;
        }

        .search-results-info strong {
            color: #ffffff;
        }

        .clear-search {
            color: #b148f3;
            text-decoration: none;
            font-weight: 600;
            margin-left: 10px;
        }

        .clear-search:hover {
            color: #9a3de0;
            text-decoration: underline;
        }

        /* Responsive design - matching admin_archiveurl */
        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
                margin: 20px;
            }

            .page-title {
                font-size: 36px;
                margin-bottom: 30px;
            }

            table, thead, tbody, th, td, tr {
                display: block;
            }

            thead tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
            }

            tr {
                margin-bottom: 15px;
                background-color: #222222;
                border-radius: 8px;
                overflow: hidden;
            }

            td {
                position: relative;
                padding-left: 50%;
                text-align: center;
                border-bottom: 1px solid #333333;
            }

            td:before {
                position: absolute;
                top: 15px;
                left: 15px;
                width: 45%;
                padding-right: 10px;
                white-space: nowrap;
                text-align: center;
                font-weight: bold;
                content: attr(data-label);
                color: #ffffff;
            }

            td:nth-of-type(1):before { content: "Token Number"; }
            td:nth-of-type(2):before { content: "Start Date"; }
            td:nth-of-type(3):before { content: "Expiry Date"; }
            td:nth-of-type(4):before { content: "Timestamp"; }
            td:nth-of-type(5):before { content: "User"; }
            td:nth-of-type(6):before { content: "Status"; }
            td:nth-of-type(7):before { content: "Delete"; }

            .top-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .search-section {
                justify-content: flex-end;
            }

            .search-input {
                width: 200px;
            }
        }

        @media (max-width: 480px) {
            .page-title {
                font-size: 28px;
            }

            td {
                padding: 12px 15px 12px 50%;
                font-size: 13px;
            }

            .btn {
                padding: 10px 20px;
                font-size: 14px;
            }

            .top-controls {
                gap: 8px;
            }

            .search-input {
                width: 160px;
                font-size: 12px;
                padding: 6px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">Generate New Token</h1>
        <div class="content">
            <!-- Top Controls: Generate Token and Search -->
            <div class="top-controls">
                <div class="generate-token-section">
                    <form id="generateTokenForm" method="POST" action="{{ url_for('generate_token') }}" style="margin: 0;">
                        <input type="submit" value="Generate Token" class="btn btn-primary" id="generateTokenBtn" />
                    </form>
                </div>

                <div class="search-section">
                    <input type="text"
                           id="search_query"
                           class="search-input"
                           value="{{ search_query or '' }}"
                           placeholder="Search by token, username, or email..."
                           autocomplete="off">
                    <button type="button" id="clearSearch" class="search-clear-btn" title="Clear search">✕</button>
                </div>
            </div>

            <!-- Flash Messages positioned below controls -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="flash-messages" style="display: block; margin: 0 0 20px 0; list-style: none; padding: 0;">
                        {% for category, message in messages %}
                            <li class="{{ category }}" style="background-color: {% if category == 'success' %}#28a745{% else %}#dc3545{% endif %}; color: white; padding: 10px 15px; margin-bottom: 10px; border-radius: 4px; font-size: 14px;" data-message="{{ message }}">{{ message }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}

            <!-- Search Results Info -->
            {% if search_query %}
            <div class="search-results-info">
                <strong>Search Results:</strong> Showing tokens matching "{{ search_query }}"
                <a href="{{ url_for('generate_token') }}" class="clear-search">Clear search</a>
            </div>
            {% endif %}
            <table>
                <thead>
                    <tr>
                        <th>Token Number</th>
                        <th>Start Date</th>
                        <th>Expiry Date</th>
                        <th>Timestamp</th>
                        <th style="text-align: center;">User</th>
                        <th style="text-align: center;">Status</th>
                        <th style="text-align: center;">Delete</th>
                    </tr>
                </thead>
                <tbody>
                    {% for token in tokens %}
                    <tr>
                        <td data-label="Token Number">
                            <span class="token-number" onclick="showOwnerModal('{{ token.token_number }}')">{{ token.token_number }}</span>
                        </td>
                        <td data-label="Start Date">{{ token.start_date.strftime('%d.%m.%Y %H:%M') if token.start_date else '' }}</td>
                        <td data-label="Expiry Date">{{ token.expiry_date.strftime('%d.%m.%Y %H:%M') if token.expiry_date else '' }}</td>
                        <td data-label="Timestamp">{{ token.timestamp.strftime('%d.%m.%Y %H:%M') if token.timestamp else '' }}</td>
                        <td data-label="User" style="text-align: center;">
                            {% if token.username %}
                                <span style="color: #b148f3; font-weight: 600;">{{ token.username }}</span>
                                <br><span style="color: #b3b3b3; font-size: 12px;">{{ token.email }}</span>
                            {% else %}
                                <span style="color: #6c757d; font-style: italic;">Unassigned</span>
                            {% endif %}
                        </td>
                        {% if not token.start_date or not token.expiry_date %}
                            <td data-label="Status" style="color: #6c757d; text-align: center;">Inactive</td>
                        {% elif token.expiry_date > now %}
                            <td data-label="Status" style="color: #28a745; font-weight: 600; text-align: center;">Active</td>
                        {% else %}
                            <td data-label="Status" style="color: #dc3545; font-weight: 600; text-align: center;">Expired</td>
                        {% endif %}
                        <td data-label="Delete" style="text-align: center;">
                            <button class="delete-btn" onclick="showDeleteModal('{{ token.token_number }}', '{{ token.token_number }}')">
                                <i class="delete-icon">🗑️</i>
                            </button>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="empty-state">
                            {% if search_query %}
                                No tokens found matching "{{ search_query }}". Try a different search term or <a href="{{ url_for('generate_token') }}" style="color: #b148f3; text-decoration: underline;">clear the search</a> to view all tokens.
                            {% else %}
                                No tokens found. Generate your first token above.
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Token Owner Modal -->
    <div class="owner-modal-overlay" id="ownerModal">
        <div class="owner-modal-content">
            <h2>Token Owner Information</h2>
            <div id="ownerContent">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="owner-modal-buttons">
                <button class="owner-back-btn" onclick="closeOwnerModal()">Back</button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal-overlay" id="deleteModal">
        <div class="modal-content">
            <h2>Delete Token Confirmation</h2>
            <p>Are you sure you want to delete this token?</p>
            <p style="margin-top: 15px; color: #ffffff; font-weight: 600;">Token: <span id="tokenToDelete" style="color: #b3b3b3;"></span></p>
            <p style="margin-top: 15px; color: #ff6b6b; font-weight: 600;">⚠️ This action cannot be undone!</p>

            <form id="deleteForm" action="{{ url_for('delete_token') }}" method="post" style="margin-top: 20px;">
                <input type="hidden" id="tokenNumberToDelete" name="token_number" value="">

                <div style="margin-bottom: 15px;">
                    <label for="adminPassword" style="display: block; color: #ffffff; margin-bottom: 8px; font-weight: 600;">Enter Admin Password:</label>
                    <input type="password" id="adminPassword" name="admin_password" required
                           style="width: 100%; padding: 10px; border: 1px solid #333333; border-radius: 6px; background-color: #1a1a1a; color: #ffffff; font-size: 14px;">
                </div>

                <div class="modal-buttons">
                    <button type="submit" class="modal-btn btn-confirm" style="background-color: #dc3545;">Delete Token</button>
                    <button type="button" class="modal-btn btn-cancel" onclick="closeDeleteModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Token Generated Modal -->
    <div class="modal-overlay" id="tokenModal">
        <div class="modal-content">
            <h2>Token Generated Successfully</h2>
            <p style="margin-bottom: 20px; color: #b3b3b3;">Your new token has been generated:</p>

            <div style="background-color: #1a1a1a; border: 1px solid #333333; border-radius: 6px; padding: 15px; margin-bottom: 20px; text-align: center;">
                <span id="generatedToken" style="font-family: 'Courier New', monospace; font-size: 18px; font-weight: 600; color: #b148f3; letter-spacing: 1px;"></span>
            </div>

            <div class="modal-buttons">
                <button type="button" class="modal-btn btn-confirm" id="copyTokenBtn" style="background-color: #28a745;">Copy Token</button>
                <button type="button" class="modal-btn btn-cancel" onclick="closeTokenModal()">Close</button>
            </div>
        </div>
    </div>

    <script>
        // Real-time search functionality
        let searchTimeout;
        const searchInput = document.getElementById('search_query');
        const clearButton = document.getElementById('clearSearch');

        // Show/hide clear button based on input content
        function toggleClearButton() {
            if (searchInput.value.trim()) {
                clearButton.classList.add('show');
            } else {
                clearButton.classList.remove('show');
            }
        }

        // Perform search with debouncing
        function performSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const searchTerm = searchInput.value.trim();
                const currentUrl = new URL(window.location);

                if (searchTerm) {
                    currentUrl.searchParams.set('search_query', searchTerm);
                } else {
                    currentUrl.searchParams.delete('search_query');
                }

                // Navigate to the new URL
                window.location.href = currentUrl.toString();
            }, 500); // 500ms delay for debouncing
        }

        // Clear search
        function clearSearch() {
            searchInput.value = '';
            toggleClearButton();
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('search_query');
            window.location.href = currentUrl.toString();
        }

        // Event listeners
        searchInput.addEventListener('input', function() {
            toggleClearButton();
            performSearch();
        });

        clearButton.addEventListener('click', clearSearch);

        // Initialize clear button visibility on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleClearButton();

            // Process flash messages to bold token numbers
            const flashMessages = document.querySelectorAll('.flash-messages li');
            flashMessages.forEach(function(msg) {
                const message = msg.textContent;
                // Check if message contains "New token generated:" pattern
                if (message.includes('New token generated:')) {
                    // Extract the token number (format: XXXX-XX-XXXX)
                    const tokenMatch = message.match(/(\d{4}-[a-f0-9]{2}-\d{4})/);
                    if (tokenMatch) {
                        const tokenNumber = tokenMatch[1];
                        const newMessage = message.replace(tokenNumber, `<strong>${tokenNumber}</strong>`);
                        msg.innerHTML = newMessage;
                    }
                }
            });

            // Setup generate token form AJAX submission
            setupTokenGeneration();
        });

        // Setup token generation functionality
        function setupTokenGeneration() {
            const form = document.getElementById('generateTokenForm');
            const button = document.getElementById('generateTokenBtn');

            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // Disable button and show loading state
                button.disabled = true;
                button.value = 'Generating...';

                // Make AJAX request
                fetch('{{ url_for("generate_token") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show token in popup
                        showTokenModal(data.token);
                    } else {
                        // Show error message
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while generating the token. Please try again.');
                })
                .finally(() => {
                    // Re-enable button
                    button.disabled = false;
                    button.value = 'Generate Token';
                });
            });
        }

        // Show delete confirmation modal
        function showDeleteModal(tokenNumber, tokenPreview) {
            document.getElementById('tokenNumberToDelete').value = tokenNumber;
            document.getElementById('tokenToDelete').textContent = tokenPreview;
            document.getElementById('adminPassword').value = '';
            document.getElementById('deleteModal').style.display = 'flex';
        }

        // Close delete modal
        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        // Show token owner modal
        function showOwnerModal(tokenNumber) {
            const modal = document.getElementById('ownerModal');
            const content = document.getElementById('ownerContent');

            // Show loading state
            content.innerHTML = '<div style="color: #b3b3b3; font-style: italic; padding: 20px;">Loading owner information...</div>';
            modal.style.display = 'flex';

            // Fetch owner information
            fetch(`/get_token_owner/${tokenNumber}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.owner) {
                            // Token has an owner
                            content.innerHTML = `
                                <div class="owner-info">
                                    <div class="info-row">
                                        <span class="info-label">Token Number:</span>
                                        <span class="info-value">${tokenNumber}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Username:</span>
                                        <span class="info-value">${data.owner.username}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Email:</span>
                                        <span class="info-value">${data.owner.email}</span>
                                    </div>
                                </div>
                            `;
                        } else {
                            // Token has no owner
                            content.innerHTML = `
                                <div class="no-owner-message">
                                    <strong>Token Number:</strong> ${tokenNumber}<br><br>
                                    This token is not currently assigned to any user.
                                </div>
                            `;
                        }
                    } else {
                        // Error occurred
                        content.innerHTML = `
                            <div class="no-owner-message" style="color: #ff6b6b;">
                                <strong>Error:</strong><br>
                                ${data.message || 'Failed to fetch owner information.'}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    content.innerHTML = `
                        <div class="no-owner-message" style="color: #ff6b6b;">
                            <strong>Error:</strong><br>
                            Failed to fetch owner information. Please try again.
                        </div>
                    `;
                    console.error('Error fetching owner information:', error);
                });
        }

        // Close owner modal
        function closeOwnerModal() {
            document.getElementById('ownerModal').style.display = 'none';
        }

        // Show token modal
        function showTokenModal(token) {
            document.getElementById('generatedToken').textContent = token;
            document.getElementById('tokenModal').style.display = 'flex';

            // Setup copy button functionality
            const copyBtn = document.getElementById('copyTokenBtn');
            copyBtn.onclick = function() {
                copyTokenToClipboard(token);
            };
        }

        // Close token modal
        function closeTokenModal() {
            document.getElementById('tokenModal').style.display = 'none';
            // Refresh the page to show the new token in the table
            window.location.reload();
        }

        // Copy token to clipboard
        function copyTokenToClipboard(token) {
            if (navigator.clipboard && window.isSecureContext) {
                // Use modern clipboard API
                navigator.clipboard.writeText(token).then(function() {
                    // Change button text temporarily
                    const copyBtn = document.getElementById('copyTokenBtn');
                    const originalText = copyBtn.textContent;
                    copyBtn.textContent = 'Copied!';
                    copyBtn.style.backgroundColor = '#28a745';

                    setTimeout(function() {
                        copyBtn.textContent = originalText;
                        copyBtn.style.backgroundColor = '#28a745';
                        closeTokenModal();
                    }, 1000);
                }).catch(function(err) {
                    console.error('Failed to copy token: ', err);
                    fallbackCopyTextToClipboard(token);
                });
            } else {
                // Fallback for older browsers
                fallbackCopyTextToClipboard(token);
            }
        }

        // Fallback copy method for older browsers
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    const copyBtn = document.getElementById('copyTokenBtn');
                    const originalText = copyBtn.textContent;
                    copyBtn.textContent = 'Copied!';

                    setTimeout(function() {
                        copyBtn.textContent = originalText;
                        closeTokenModal();
                    }, 1000);
                } else {
                    alert('Failed to copy token. Please copy manually: ' + text);
                }
            } catch (err) {
                alert('Failed to copy token. Please copy manually: ' + text);
            }

            document.body.removeChild(textArea);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const deleteModal = document.getElementById('deleteModal');
            const ownerModal = document.getElementById('ownerModal');
            const tokenModal = document.getElementById('tokenModal');

            if (event.target === deleteModal) {
                closeDeleteModal();
            } else if (event.target === ownerModal) {
                closeOwnerModal();
            } else if (event.target === tokenModal) {
                closeTokenModal();
            }
        }
    </script>

 <p style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; bottom: 0px;  margin-bottom: 15px; width: 100%;">© URLCHECK 2025.All rights reserved</p>
</body>
</html>

{% endblock %}
