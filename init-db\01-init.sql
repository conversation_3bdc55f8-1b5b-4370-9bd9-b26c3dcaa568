-- Initialize URL Scanner Database
-- This script will be executed when MySQL container starts for the first time

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS urlscanner;
USE urlscanner;

-- Set SQL mode and character set
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";
SET NAMES utf8mb4;

-- Table structure for table `admin`
CREATE TABLE IF NOT EXISTS `admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `password` varchar(100) NOT NULL,
  `profile_picture` longblob DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `users`
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `token` varchar(50) DEFAULT NULL,
  `profile_picture` longblob DEFAULT NULL,
  `failed_attempts` int(11) DEFAULT 0,
  `cooldown_until` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `token`
CREATE TABLE IF NOT EXISTS `token` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token_number` varchar(50) NOT NULL,
  `start_date` date DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `token_number` (`token_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `contact_us`
CREATE TABLE IF NOT EXISTS `contact_us` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `subject` varchar(150) NOT NULL,
  `message` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `scan_results`
CREATE TABLE IF NOT EXISTS `scan_results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `url` text NOT NULL,
  `domain` varchar(255) DEFAULT NULL,
  `ip_address` varchar(100) DEFAULT NULL,
  `protocol` varchar(10) DEFAULT NULL,
  `creation_date` varchar(100) DEFAULT NULL,
  `updated_date` varchar(100) DEFAULT NULL,
  `expiry_date` varchar(100) DEFAULT NULL,
  `age` varchar(100) DEFAULT NULL,
  `registrar` varchar(255) DEFAULT NULL,
  `url_length` int(11) DEFAULT NULL,
  `classification` varchar(20) DEFAULT NULL,
  `note` text DEFAULT NULL,
  `timestamp` datetime DEFAULT current_timestamp(),
  `sender` varchar(100) DEFAULT 'guest',
  `ml_prediction` varchar(255) DEFAULT NULL,
  `ml_note` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_sender` (`sender`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_classification` (`classification`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `url_breakdown`
CREATE TABLE IF NOT EXISTS `url_breakdown` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `scan_url` text NOT NULL,
  `part` varchar(50) NOT NULL,
  `fragment_value` text DEFAULT NULL,
  `score` int(11) DEFAULT NULL,
  `reason` text DEFAULT NULL,
  `mitigation` text DEFAULT NULL,
  `sender` varchar(100) DEFAULT 'guest',
  `timestamp` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_scan_url` (`scan_url`(255)),
  KEY `idx_sender` (`sender`),
  KEY `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
