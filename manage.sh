#!/bin/bash

# URL Scanner Management Script
# This script provides various management commands for the URL Scanner application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "URL Scanner Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start       Start all services"
    echo "  stop        Stop all services"
    echo "  restart     Restart all services"
    echo "  status      Show service status"
    echo "  logs        Show logs for all services"
    echo "  logs-web    Show logs for web service only"
    echo "  logs-db     Show logs for database service only"
    echo "  backup      Backup database"
    echo "  restore     Restore database from backup"
    echo "  shell-web   Open shell in web container"
    echo "  shell-db    Open MySQL shell"
    echo "  update      Update and rebuild services"
    echo "  clean       Clean up unused Docker resources"
    echo "  help        Show this help message"
}

# Function to start services
start_services() {
    print_status "Starting services..."
    docker-compose up -d
    print_success "Services started"
}

# Function to stop services
stop_services() {
    print_status "Stopping services..."
    docker-compose down
    print_success "Services stopped"
}

# Function to restart services
restart_services() {
    print_status "Restarting services..."
    docker-compose restart
    print_success "Services restarted"
}

# Function to show status
show_status() {
    print_status "Service Status:"
    docker-compose ps
}

# Function to show logs
show_logs() {
    if [ "$1" = "web" ]; then
        docker-compose logs -f web
    elif [ "$1" = "db" ]; then
        docker-compose logs -f mysql
    else
        docker-compose logs -f
    fi
}

# Function to backup database
backup_database() {
    print_status "Creating database backup..."
    
    # Create backup directory if it doesn't exist
    mkdir -p backups
    
    # Generate backup filename with timestamp
    backup_file="backups/urlscanner_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # Create backup
    docker-compose exec mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD:-rootpassword123} urlscanner > "$backup_file"
    
    print_success "Database backup created: $backup_file"
}

# Function to restore database
restore_database() {
    if [ -z "$1" ]; then
        print_error "Please specify backup file to restore"
        echo "Usage: $0 restore <backup_file>"
        exit 1
    fi
    
    if [ ! -f "$1" ]; then
        print_error "Backup file not found: $1"
        exit 1
    fi
    
    print_warning "This will overwrite the current database. Are you sure? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_status "Restore cancelled"
        exit 0
    fi
    
    print_status "Restoring database from: $1"
    docker-compose exec -T mysql mysql -u root -p${MYSQL_ROOT_PASSWORD:-rootpassword123} urlscanner < "$1"
    print_success "Database restored successfully"
}

# Function to open web container shell
shell_web() {
    print_status "Opening shell in web container..."
    docker-compose exec web /bin/bash
}

# Function to open database shell
shell_db() {
    print_status "Opening MySQL shell..."
    docker-compose exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD:-rootpassword123} urlscanner
}

# Function to update services
update_services() {
    print_status "Updating and rebuilding services..."
    docker-compose down
    docker-compose build --no-cache
    docker-compose up -d
    print_success "Services updated and restarted"
}

# Function to clean up Docker resources
clean_docker() {
    print_status "Cleaning up unused Docker resources..."
    docker system prune -f
    docker volume prune -f
    print_success "Docker cleanup completed"
}

# Main function
main() {
    case "${1:-help}" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        logs-web)
            show_logs web
            ;;
        logs-db)
            show_logs db
            ;;
        backup)
            backup_database
            ;;
        restore)
            restore_database "$2"
            ;;
        shell-web)
            shell_web
            ;;
        shell-db)
            shell_db
            ;;
        update)
            update_services
            ;;
        clean)
            clean_docker
            ;;
        help|*)
            show_usage
            ;;
    esac
}

# Run main function with all arguments
main "$@"
