#!/bin/bash

# URL Scanner Monitoring Script
# This script monitors the health and performance of the URL Scanner application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check service health
check_service_health() {
    local service_name=$1
    local health_url=$2
    
    print_status "Checking $service_name health..."
    
    if curl -f -s "$health_url" > /dev/null; then
        print_success "$service_name is healthy"
        return 0
    else
        print_error "$service_name is unhealthy"
        return 1
    fi
}

# Function to check container status
check_container_status() {
    print_status "Checking container status..."
    
    local containers=("urlscanner_web" "urlscanner_mysql" "urlscanner_phpmyadmin")
    local all_healthy=true
    
    for container in "${containers[@]}"; do
        if docker ps --filter "name=$container" --filter "status=running" | grep -q "$container"; then
            print_success "$container is running"
        else
            print_error "$container is not running"
            all_healthy=false
        fi
    done
    
    return $all_healthy
}

# Function to check resource usage
check_resource_usage() {
    print_status "Checking resource usage..."
    
    echo ""
    echo "=== Docker Container Resource Usage ==="
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"
    echo ""
}

# Function to check disk usage
check_disk_usage() {
    print_status "Checking disk usage..."
    
    echo ""
    echo "=== Docker Volume Usage ==="
    docker system df
    echo ""
    
    echo "=== Host Disk Usage ==="
    df -h
    echo ""
}

# Function to check logs for errors
check_logs_for_errors() {
    print_status "Checking recent logs for errors..."
    
    echo ""
    echo "=== Recent Web Service Errors ==="
    docker-compose logs --tail=50 web | grep -i error || echo "No recent errors found in web service"
    
    echo ""
    echo "=== Recent Database Errors ==="
    docker-compose logs --tail=50 mysql | grep -i error || echo "No recent errors found in database service"
    echo ""
}

# Function to test application endpoints
test_endpoints() {
    print_status "Testing application endpoints..."
    
    local base_url="http://localhost:5000"
    local endpoints=("/" "/health")
    
    for endpoint in "${endpoints[@]}"; do
        local url="$base_url$endpoint"
        if curl -f -s "$url" > /dev/null; then
            print_success "Endpoint $endpoint is accessible"
        else
            print_error "Endpoint $endpoint is not accessible"
        fi
    done
}

# Function to check database connectivity
check_database() {
    print_status "Checking database connectivity..."
    
    if docker-compose exec mysql mysqladmin ping -h localhost --silent; then
        print_success "Database is accessible"
        
        # Check database size
        echo ""
        echo "=== Database Information ==="
        docker-compose exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD:-rootpassword123} -e "
            SELECT 
                table_schema AS 'Database',
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
            FROM information_schema.tables 
            WHERE table_schema = 'urlscanner'
            GROUP BY table_schema;
        " 2>/dev/null || echo "Could not retrieve database size information"
        echo ""
    else
        print_error "Database is not accessible"
    fi
}

# Function to generate monitoring report
generate_report() {
    local report_file="monitoring_report_$(date +%Y%m%d_%H%M%S).txt"
    
    print_status "Generating monitoring report: $report_file"
    
    {
        echo "URL Scanner Monitoring Report"
        echo "Generated: $(date)"
        echo "========================================"
        echo ""
        
        echo "=== Container Status ==="
        docker-compose ps
        echo ""
        
        echo "=== Resource Usage ==="
        docker stats --no-stream
        echo ""
        
        echo "=== Disk Usage ==="
        docker system df
        echo ""
        
        echo "=== Recent Logs (Last 100 lines) ==="
        docker-compose logs --tail=100
        
    } > "$report_file"
    
    print_success "Report generated: $report_file"
}

# Function to show monitoring dashboard
show_dashboard() {
    clear
    echo "========================================"
    echo "    URL Scanner Monitoring Dashboard"
    echo "========================================"
    echo "Last updated: $(date)"
    echo ""
    
    check_container_status
    echo ""
    
    test_endpoints
    echo ""
    
    check_database
    echo ""
    
    check_resource_usage
}

# Function to continuous monitoring
continuous_monitoring() {
    local interval=${1:-30}
    
    print_status "Starting continuous monitoring (interval: ${interval}s)"
    print_status "Press Ctrl+C to stop"
    
    while true; do
        show_dashboard
        sleep "$interval"
    done
}

# Function to show usage
show_usage() {
    echo "URL Scanner Monitoring Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  status      Show current status of all services"
    echo "  health      Check health of all services"
    echo "  resources   Show resource usage"
    echo "  logs        Check logs for errors"
    echo "  endpoints   Test application endpoints"
    echo "  database    Check database connectivity and info"
    echo "  report      Generate comprehensive monitoring report"
    echo "  dashboard   Show monitoring dashboard"
    echo "  watch       Continuous monitoring (default: 30s interval)"
    echo "  help        Show this help message"
    echo ""
    echo "Options for 'watch' command:"
    echo "  -i, --interval SECONDS    Set monitoring interval (default: 30)"
}

# Main function
main() {
    case "${1:-help}" in
        status)
            check_container_status
            ;;
        health)
            check_service_health "Web Application" "http://localhost:5000/health"
            check_database
            ;;
        resources)
            check_resource_usage
            check_disk_usage
            ;;
        logs)
            check_logs_for_errors
            ;;
        endpoints)
            test_endpoints
            ;;
        database)
            check_database
            ;;
        report)
            generate_report
            ;;
        dashboard)
            show_dashboard
            ;;
        watch)
            local interval=30
            if [ "$2" = "-i" ] || [ "$2" = "--interval" ]; then
                interval=${3:-30}
            fi
            continuous_monitoring "$interval"
            ;;
        help|*)
            show_usage
            ;;
    esac
}

# Run main function with all arguments
main "$@"
