{% extends "header_register.html" %}
{% block content %}

<!DOCTYPE html>
<html>
<head>
    <title>Register</title>
 <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark mode base styles */
body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}

/* Input field styling for dark mode */
input[type="text"],
input[type="text"]:focus,
input[type="text"]:active,
input[type="text"]:hover,
input[type="password"],
input[type="password"]:focus,
input[type="email"],
input[type="email"]:focus,
textarea,
textarea:focus,
select,
select:focus {
    background-color: #333333 !important;
    color: #ffffff !important;
    border: 1px solid #444444;
    outline-color: #1e3c72;
}

/* Override browser autofill styles which often use white backgrounds */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
    -webkit-text-fill-color: #ffffff !important;
    transition: background-color 5000s ease-in-out 0s;
}

        /* Main content container */

        /* Main container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 0px;
            margin-bottom: 178px;
        }

        /* Page title */
        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 30px;
            text-align: center;
            letter-spacing: -0.04em;

        }

         .content p {
            color: #b3b3b3;
            font-size: 14px;

            text-align: center;
            width: 360px;
            margin: 0 auto 25px auto;


        }

        form {
            display: flex;
            flex-direction: column;
            width: 33%;
            margin: auto;
        }

        label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #b3b3b3;
            font-size: 12px;
        }

        input[type="text"],
        input[type="email"],
        input[type="password"] {
            padding: 10px 12px;
            border: 1px solid #333;
            background-color: #282828;

            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
            border-radius: 30px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        input:focus {
            border-color: #5dcef0;
            box-shadow: 0 0 0 2px #5dcef0;
            outline: none;
        }

        .password-section {
            display: flex;
            flex-direction: column;
        }

        .password-input-wrapper {
            position: relative;
            display: block;
            width: 100%;
        }

        /* Tooltip styles */
        .tooltip {
            position: relative;
            display: block;
            width: 100%;
        }

        .tooltip input {
            width: 100%;
            box-sizing: border-box;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 280px;
            background-color: #333333;
            color: #ffffff;
            text-align: left;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 100%;
            left: 60px;
            margin-bottom: 10px;
            opacity: 0;
            transition: opacity 0.3s;
            border: 1px solid #5dcef0;
            font-size: 12px;
            line-height: 1.4;
        }

        .tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 20px;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #5dcef0 transparent transparent transparent;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .tooltip-criteria {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .tooltip-criteria li {
            margin: 5px 0;
            color: #cb1023;
            font-size: 11px;
        }

        .tooltip-criteria li.valid {
            color: #1DB954;
        }

        .tooltip-criteria li::before {
            content: "✗ ";
            margin-right: 5px;
        }

        .tooltip-criteria li.valid::before {
            content: "✓ ";
        }

        /* Question mark tooltip styles */
        .label-with-tooltip {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .question-tooltip {
            position: relative;
            bottom: 4px;;
            display: inline-block;
            cursor: help;
        }

        .question-icon {
            width: 13px;
            height: 13px;
            background-color: #4aa4c0;
            color: #000000;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            transition: background-color 0.3s ease;
        }

        .question-icon:hover {
            background-color: #3d889f;
        }

        .question-tooltip .question-tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #333333;
            color: #ffffff;
            text-align: left;
            border-radius: 6px;
            padding: 12px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            border: 1px solid #5dcef0;
            font-size: 13px;
            line-height: 1.4;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .question-tooltip .question-tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #5dcef0 transparent transparent transparent;
        }

        .question-tooltip:hover .question-tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        button {
            background-color: #4aa4c0;
            color: rgb(0, 0, 0);
            border: none;
            border-radius: 30px;
            padding: 14px 0;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 10px;
        }

        button:disabled {
            background-color: #333333;
            cursor: not-allowed;
        }

        button:hover:enabled {
            background-color: #5dcef0;
            transform: scale(1.02);
        }

        button:disabled {
            background-color: #666666 !important;
            cursor: not-allowed !important;
            opacity: 0.6 !important;
            transform: none !important;
        }

        .login-link {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
        }

        .login-link a {
            color: #b3b3b3;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .login-link a:hover {
            color: #4aa4c0;
        }

        .guide-link {
            margin-top: -19px;
            margin-bottom: 18px;
            text-align: right;
        }

        .guide-link a {
            text-decoration: none;
            color: #b3b3b3;
            font-size: 12px;
            transition: color 0.3s ease;
        }

        .guide-link a:hover {
            color: #4aa4c0;
        }

        /* Flash messages container */
        .flash-messages {
            position: fixed;
            top: 85px;
            right: -2px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .flash-messages li {
            background-color: #ff0019a8;
            color: white;
            padding: 15px 25px;
            margin-top: 10px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            font-size: 14px;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 5s forwards;
            cursor: pointer;
            min-width: 250px;
        }

        /* Animation for slide in from right and fade out */
        @keyframes slideInOut {
            0% {
                opacity: 0;
                transform: translateX(100%);
            }
            10% {
                opacity: 1;
                transform: translateX(0);
            }
            90% {
                opacity: 1;
                transform: translateX(0);
            }
            100% {
                opacity: 0;
                transform: translateX(100%);
            }
        }
        /* reCAPTCHA styling */
        .recaptcha-container {
            display: flex;
            justify-content: center;
            margin: 5px 0;
            transform: scale(0.9);
            transform-origin: center;
        }

        /* Standardized Mobile Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 0 20px;
                margin-bottom: 120px;
            }

            .content {
                padding: 0;
            }

            .page-title {
                font-size: 32px;
                margin-bottom: 30px;
                margin-top: 50px;
            }

            .register-form {
                width: 80%;
            }

            .register-form button {
                padding: 14px;
                font-size: 16px;
            }

            .flash-messages li {
                min-width: 250px;
                padding: 15px 20px;
                font-size: 14px;
            }

            .question-tooltip .question-tooltiptext {
                width: 280px;
                margin-left: -140px;
                font-size: 12px;
            }
        }

        @media (max-width: 600px) {
            .container {
                margin: 15px auto;
                padding: 0 15px;
                margin-bottom: 100px;
            }

            .page-title {
                font-size: 28px;
                margin-bottom: 25px;
            }

            .register-form {
                width: 90%;
            }

            .register-form button {
                padding: 12px;
                font-size: 15px;
            }

            .flash-messages li {
                min-width: 200px;
                padding: 12px 20px;
                font-size: 13px;
            }

            .question-tooltip .question-tooltiptext {
                width: 260px;
                margin-left: -130px;
                font-size: 11px;
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px auto;
                padding: 0 10px;
                margin-bottom: 80px;
            }

            .page-title {
                font-size: 24px;
                margin-bottom: 20px;
            }

            .register-form {
                width: 95%;
            }

            .register-form button {
                padding: 10px;
                font-size: 14px;
            }

            .flash-messages li {
                min-width: 180px;
                padding: 10px 15px;
                font-size: 12px;
            }

            .question-tooltip .question-tooltiptext {
                width: 240px;
                margin-left: -120px;
                font-size: 10px;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
<div class="content">
        <h1 class="page-title">Sign Up</h1>
        <p>To complete your registration, a valid token is required. You can obtain it on the Plan page.</p>

        {% with messages = get_flashed_messages() %}
            {% if messages %}
              <ul class="flash-messages" id="flash-messages">
                {% for message in messages %}
                  <li>{{ message }}</li>
                {% endfor %}
              </ul>
            {% endif %}
        {% endwith %}

        <form method="POST" class="register-form" onsubmit="return validateForm()">
            <label for="username">Create Username:</label>
            <input type="text" name="username" id="username" placeholder="Create your username"required>

            <label for="email">Email:</label>
            <input type="email" name="email" id="email" placeholder="Enter your email"required>

            <div class="password-section">
                <label for="password">Password:</label>
                <div class="tooltip">
                    <input type="password" name="password" id="password" placeholder="Create new password" oninput="validatePassword()" required>
                    <span class="tooltiptext">
                        <strong>Password Requirements:</strong>
                        <ul class="tooltip-criteria">
                            <li id="uppercase">At least 1 uppercase letter (A-Z)</li>
                            <li id="lowercase">At least 1 lowercase letter (a-z)</li>
                            <li id="digit">At least 1 digit (0-9)</li>
                            <li id="symbol">At least 1 symbol (!@#$%^&*)</li>
                            <li id="length">At least 12 characters</li>
                            <li id="match">Passwords match</li>
                        </ul>
                    </span>
                </div>

                <label for="confirm_password">Confirm Password:</label>
                <div class="tooltip">
                    <input type="password" name="confirm_password" id="confirm_password" placeholder="Confirm your password" oninput="validatePassword()" required>
                    <span class="tooltiptext">
                        <strong>Confirm Password:</strong>
                        <ul class="tooltip-criteria">
                            <li id="match-confirm">Passwords must match</li>
                            <li id="not-empty-confirm">Field cannot be empty</li>
                        </ul>
                    </span>
                </div>
            </div>

            <div class="label-with-tooltip">
                <label for="token">Token:</label>
                <div class="question-tooltip">
                    <div class="question-icon">?</div>
                    <span class="question-tooltiptext">
                        Registration on URLCHECK requires a Token. Visit the Plan page to purchase Token.
                    </span>
                </div>
            </div>
            <input type="text" name="token" id="token" placeholder="Enter your token" required>

            <div class="guide-link">
                <a href="/guide#how-to-register-account">How to register account?</a>
            </div>

            <!-- reCAPTCHA widget -->
            <div class="g-recaptcha" data-sitekey="{{ recaptcha_site_key }}" data-theme="dark" style="margin: 20px 0;"></div>

            <button type="submit" id="submitBtn" {% if cooldown_info and cooldown_info.is_in_cooldown %}disabled{% endif %}>
                {% if cooldown_info and cooldown_info.is_in_cooldown %}
                    Disabled (<span id="cooldown-timer">{{ cooldown_info.remaining_time }}</span>s)
                {% else %}
                    Sign Up
                {% endif %}
            </button>
        </form>

        <div class="login-link">
            <a href="{{ url_for('signin_page') }}">Already have an account? Login</a>
        </div>
</div>
    <script>
        function validatePassword() {
            const password = document.getElementById("password").value;
            const confirmPassword = document.getElementById("confirm_password").value;

            // Regular Expressions for Password
            const hasUpper = /[A-Z]/.test(password);
            const hasLower = /[a-z]/.test(password);
            const hasDigit = /[0-9]/.test(password);
            const hasSymbol = /[!@#$%^&*]/.test(password);
            const isLongEnough = password.length >= 12;
            const passwordsMatch = password === confirmPassword && password.length > 0;

            // Toggle valid/invalid classes for Password tooltip (visual feedback only)
            document.getElementById("uppercase").classList.toggle("valid", hasUpper);
            document.getElementById("lowercase").classList.toggle("valid", hasLower);
            document.getElementById("digit").classList.toggle("valid", hasDigit);
            document.getElementById("symbol").classList.toggle("valid", hasSymbol);
            document.getElementById("length").classList.toggle("valid", isLongEnough);
            document.getElementById("match").classList.toggle("valid", passwordsMatch);

            // Toggle valid/invalid classes for Confirm Password tooltip (visual feedback only)
            const confirmNotEmpty = confirmPassword.length > 0;
            document.getElementById("match-confirm").classList.toggle("valid", passwordsMatch);
            document.getElementById("not-empty-confirm").classList.toggle("valid", confirmNotEmpty);
        }

        function validateForm() {
            const password = document.getElementById("password").value;
            const confirmPassword = document.getElementById("confirm_password").value;

            if (password !== confirmPassword) {
                alert("Passwords do not match!");
                return false;
            }

            // Check if reCAPTCHA is completed
            const recaptchaResponse = grecaptcha.getResponse();
            if (recaptchaResponse.length === 0) {
                alert('Please complete the reCAPTCHA verification.');
                return false;
            }

            return true;
        }
        const registerForm = document.querySelector('.register-form');
        const submitBtn = document.getElementById('submitBtn');

        registerForm?.addEventListener('submit', function(e) {
            // Check if reCAPTCHA is completed
            const recaptchaResponse = grecaptcha.getResponse();
            if (recaptchaResponse.length === 0) {
                e.preventDefault();
                alert('Please complete the reCAPTCHA verification.');
                return false;
            }

            if (submitBtn && !submitBtn.disabled) {
                submitBtn.disabled = true;
                submitBtn.textContent = 'Registering...';
            }
        });



        // Remove flash message on click
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-messages li');
            flashMessages.forEach(function(msg) {
                msg.addEventListener('click', function() {
                    msg.style.animation = 'slideOut 0.5s forwards';
                    setTimeout(() => {
                        msg.remove();
                    }, 500);
                });
            });
        });

        // Add slideOut animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideOut {
                0% {
                    opacity: 1;
                    transform: translateX(0);
                }
                100% {
                    opacity: 0;
                    transform: translateX(100%);
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    <!-- Cooldown timer script -->
    {% if cooldown_info and cooldown_info.is_in_cooldown %}
    <script>
        (function() {
            let remainingTime = {{ cooldown_info.remaining_time }};
            const cooldownTimer = document.getElementById('cooldown-timer');
            const submitButton = document.getElementById('submitBtn');

            const countdown = setInterval(function() {
                remainingTime--;
                if (cooldownTimer) {
                    cooldownTimer.textContent = remainingTime;
                }

                if (remainingTime <= 0) {
                    clearInterval(countdown);
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.innerHTML = 'Sign Up';
                    }
                    // Remove flash message when cooldown ends
                    const flashMessages = document.querySelectorAll('.flash-messages li');
                    flashMessages.forEach(function(msg) {
                        msg.style.animation = 'slideOut 0.5s forwards';
                        setTimeout(() => {
                            msg.remove();
                        }, 500);
                    });
                }
            }, 1000);
        })();
    </script>
    {% endif %}

<script src="https://www.google.com/recaptcha/api.js" async defer></script>

</div>
</body>
</html>

{% endblock %}