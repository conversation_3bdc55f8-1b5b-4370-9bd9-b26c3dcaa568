# URL Scanner - Docker Deployment

A comprehensive URL scanning application built with Flask and Machine Learning, now containerized with Docker for easy deployment and scalability.

## 🚀 Features

- **URL Analysis**: Advanced URL scanning using Machine Learning models
- **User Management**: Registration, authentication, and profile management
- **Admin Dashboard**: Administrative interface for user and system management
- **Email Verification**: OTP-based email verification system
- **Scan History**: Complete history of URL scans with detailed analysis
- **Security Features**: Brute force protection, secure sessions, and input validation
- **Docker Ready**: Fully containerized with Docker Compose

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Docker**: Version 20.10 or higher
- **Docker Compose**: Version 2.0 or higher
- **Git**: For cloning the repository

### Installing Docker

#### Windows
1. Download Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop)
2. Run the installer and follow the setup wizard
3. Restart your computer when prompted

#### macOS
1. Download Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop)
2. Drag Docker to your Applications folder
3. Launch Docker from Applications

#### Linux (Ubuntu/Debian)
```bash
# Update package index
sudo apt-get update

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Add user to docker group
sudo usermod -aG docker $USER
```

## 🛠️ Quick Start

### 1. Clone the Repository
```bash
git clone <your-repository-url>
cd syafiqq
```

### 2. Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit configuration (optional - defaults work for development)
nano .env
```

### 3. Deploy with One Command
```bash
# Make scripts executable (Linux/macOS)
chmod +x deploy.sh manage.sh monitor.sh

# Deploy the application
./deploy.sh
```

### 4. Access the Application
- **URL Scanner**: http://localhost:5000
- **phpMyAdmin**: http://localhost:8080
- **Default Admin**: username: `admin`, password: `admin123`

## 📁 Project Structure

```
syafiqq/
├── app.py                 # Main Flask application
├── scanning.py            # URL scanning logic
├── requirements.txt       # Python dependencies
├── Dockerfile            # Docker container configuration
├── docker-compose.yml    # Multi-container setup
├── .env                  # Environment variables
├── .env.example          # Environment template
├── deploy.sh             # Deployment script
├── manage.sh             # Management script
├── monitor.sh            # Monitoring script
├── init-db/              # Database initialization
│   ├── 01-init.sql       # Database schema
│   └── 02-seed-data.sql  # Initial data
├── templates/            # HTML templates
├── static/               # Static files (CSS, JS, images)
├── training_model/       # ML models
├── features_extraction/  # Feature extraction files
└── dataset/              # Training datasets
```

## ⚙️ Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Database Configuration
DB_HOST=mysql
DB_USER=user1
DB_PASSWORD=newpassword123
DB_NAME=urlscanner
MYSQL_ROOT_PASSWORD=rootpassword123

# Flask Configuration
FLASK_ENV=production
SECRET_KEY=your_secret_key_change_this_in_production

# Email Configuration (for OTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password

# reCAPTCHA Configuration
RECAPTCHA_SITE_KEY=your_recaptcha_site_key
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key
```

### Security Configuration

For production deployment:

1. **Change Default Passwords**:
   - Update `DB_PASSWORD` and `MYSQL_ROOT_PASSWORD`
   - Change admin password after first login

2. **Configure Email**:
   - Set up Gmail App Password for OTP functionality
   - Update `EMAIL_USER` and `EMAIL_PASSWORD`

3. **Set up reCAPTCHA**:
   - Get keys from [Google reCAPTCHA](https://www.google.com/recaptcha/)
   - Update `RECAPTCHA_SITE_KEY` and `RECAPTCHA_SECRET_KEY`

4. **Generate Secret Key**:
   ```bash
   python -c "import secrets; print(secrets.token_hex(32))"
   ```

## 🔧 Management Commands

### Using the Management Script

```bash
# Start services
./manage.sh start

# Stop services
./manage.sh stop

# Restart services
./manage.sh restart

# View service status
./manage.sh status

# View logs
./manage.sh logs
./manage.sh logs-web    # Web service only
./manage.sh logs-db     # Database only

# Database operations
./manage.sh backup      # Backup database
./manage.sh restore backup_file.sql

# Container access
./manage.sh shell-web   # Access web container
./manage.sh shell-db    # Access database

# Maintenance
./manage.sh update      # Update and rebuild
./manage.sh clean       # Clean unused resources
```

### Manual Docker Commands

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f

# Rebuild and restart
docker-compose up --build -d

# Scale web service
docker-compose up -d --scale web=3
```

## 📊 Monitoring

### Using the Monitoring Script

```bash
# Check overall status
./monitor.sh status

# Health check
./monitor.sh health

# Resource usage
./monitor.sh resources

# Check for errors in logs
./monitor.sh logs

# Test endpoints
./monitor.sh endpoints

# Database information
./monitor.sh database

# Generate comprehensive report
./monitor.sh report

# Real-time dashboard
./monitor.sh dashboard

# Continuous monitoring (30s interval)
./monitor.sh watch

# Custom interval monitoring
./monitor.sh watch -i 60
```

### Health Check Endpoints

- **Application Health**: `GET /health`
  ```json
  {
    "status": "healthy",
    "database": "connected",
    "timestamp": "2024-01-01T12:00:00"
  }
  ```

## 🔍 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using the port
netstat -tulpn | grep :5000

# Stop conflicting services or change ports in docker-compose.yml
```

#### 2. Database Connection Issues
```bash
# Check database logs
./manage.sh logs-db

# Restart database service
docker-compose restart mysql

# Verify database is healthy
./monitor.sh database
```

#### 3. Permission Issues (Linux/macOS)
```bash
# Fix script permissions
chmod +x *.sh

# Fix file ownership
sudo chown -R $USER:$USER .
```

#### 4. Memory Issues
```bash
# Check resource usage
./monitor.sh resources

# Increase Docker memory limit in Docker Desktop settings
# Or reduce worker count in Dockerfile
```

### Logs and Debugging

```bash
# View all logs
docker-compose logs

# Follow logs in real-time
docker-compose logs -f

# View specific service logs
docker-compose logs web
docker-compose logs mysql

# Check container status
docker-compose ps

# Inspect container details
docker inspect urlscanner_web
```

## 🚀 Production Deployment

### Security Checklist

- [ ] Change all default passwords
- [ ] Configure proper email settings
- [ ] Set up reCAPTCHA
- [ ] Generate secure secret key
- [ ] Enable HTTPS (use reverse proxy like Nginx)
- [ ] Set up regular backups
- [ ] Configure log rotation
- [ ] Set up monitoring and alerting
- [ ] Update firewall rules
- [ ] Regular security updates

### Performance Optimization

1. **Database Optimization**:
   ```sql
   # Add indexes for better performance
   CREATE INDEX idx_scan_results_timestamp ON scan_results(timestamp);
   CREATE INDEX idx_users_email ON users(email);
   ```

2. **Application Scaling**:
   ```bash
   # Scale web service
   docker-compose up -d --scale web=3
   
   # Use load balancer (Nginx)
   # Configure session storage (Redis)
   ```

3. **Resource Limits**:
   ```yaml
   # Add to docker-compose.yml
   deploy:
     resources:
       limits:
         memory: 512M
         cpus: '0.5'
   ```

## 📝 API Documentation

### Health Check
- **GET** `/health` - Application health status

### Main Application
- **GET** `/` - Home page
- **POST** `/result` - URL scanning
- **POST** `/details` - Detailed scan results
- **GET** `/archiveurl` - Scan history

### Authentication
- **GET/POST** `/Sign-In` - User login
- **GET/POST** `/Sign-Up` - User registration
- **GET** `/profile` - User profile

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with Docker
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review Docker and application logs

---

**Note**: This application includes Machine Learning models for URL classification. Ensure you have sufficient system resources (minimum 2GB RAM recommended) for optimal performance.

## 🔄 Backup and Recovery

### Automated Backups
```bash
# Create backup
./manage.sh backup

# Restore from backup
./manage.sh restore backups/urlscanner_backup_20240101_120000.sql
```

### Manual Database Operations
```bash
# Export database
docker-compose exec mysql mysqldump -u root -p urlscanner > backup.sql

# Import database
docker-compose exec -T mysql mysql -u root -p urlscanner < backup.sql
```

## 🌐 Reverse Proxy Setup (Nginx)

For production with HTTPS:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```
