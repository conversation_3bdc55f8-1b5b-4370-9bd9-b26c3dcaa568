{% extends "header_admin.html" %}

{% block content %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark theme base styles */
        body {
            background: linear-gradient(135deg, #141719 0%, #121212 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        /* Input field styling for dark mode */
        input[type="text"],
        input[type="text"]:focus,
        input[type="text"]:active,
        input[type="text"]:hover,
        input[type="password"],
        input[type="password"]:focus,
        input[type="email"],
        input[type="email"]:focus,
        input[type="file"],
        input[type="file"]:focus,
        textarea,
        textarea:focus,
        select,
        select:focus {
            background-color: #333333 !important;
            color: #ffffff !important;
            border: 1px solid #444444;
            outline-color: #b148f3;
        }

        /* Override browser autofill styles */
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
            -webkit-text-fill-color: #ffffff !important;
            transition: background-color 5000s ease-in-out 0s;
        }

        /* Main container */
        .content {
            max-width: 800px;
            margin: 40px auto;
            padding: 0 20px;
        }

        /* Page title */
        .page-title {
            font-family: 'Roboto', sans-serif;
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 20px;
            text-align: center;
            letter-spacing: -0.04em;
        }
        /* Profile container */
        .profile-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
            padding: 32px;
            background-color: #12121200;
            border-radius: 8px;
        }

        /* Image container */
        .image-container {
            margin-bottom: 24px;
            position: relative;
        }

        /* Profile picture */
        .profile-picture {
            border-radius: 50%;
            width: 160px;
            height: 160px;
            object-fit: cover;
            border: 4px solid #b148f3;
            box-shadow: 0 8px 24px rgba(177, 72, 243, 0.3);
        }

        /* File input styling */
        #profile_picture {
            background-color: transparent;
            color: #b3b3b3;
            border: 1px solid #333333;
            border-radius: 500px;
            padding: 8px 20px;
            margin-top: 0px;
            font-size: 8px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }

        #profile_picture::-webkit-file-upload-button {
            background-color: #b148f3;
            color: #ffffff;
            border: none;
            border-radius: 500px;
            padding: 8px 24px;
            margin-right: 16px;
            font-weight: 700;
            cursor: pointer;
            text-transform: uppercase;
            font-size: 8px;
        }

        #profile_picture:hover {
            border-color: #b148f3;
            transform: scale(1.04);
        }

        /* Form section */
        .form-section {
            background-color: #333333;
            border-radius: 8px;
            padding: 10px 30px;
            margin-bottom: 24px;
        }

        .form-section h2 {
            font-size: 24px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 24px;
            border-bottom: 1px solid #282828;
            padding-bottom: 16px;
        }

        /* Form group styling */
        .form-group {
            margin-bottom: 2px;
        }

        .form-group.row {
            display: flex;
            align-items: center;
            gap: 24px;
            padding: 16px 0;
            border-bottom: 1px solid #282828;
        }

        .form-group.row:last-child {
            border-bottom: none;
        }

        /* Form label */
        .form-label {
            min-width: 140px;
            font-weight: 400;
            color: #b3b3b3;
            font-size: 16px;
        }

        /* Form info display (read-only fields) */
        .form-info {
            flex: 1;
            font-size: 16px;
            color: #ffffff;
            font-weight: 400;
        }

        .form-info.readonly {
            color: #b3b3b3;
            font-style: italic;
        }

        /* Form input */
        .form-input {
            flex: 1;
            padding: 12px 16px;
            font-size: 16px;
            border: 1px solid #333333;
            border-radius: 4px;
            background-color: #121212;
            color: #ffffff;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #b148f3;
            box-shadow: 0 0 0 3px rgba(177, 72, 243, 0.1);
        }

        .form-input:disabled {
            background-color: #282828;
            color: #333333;
            cursor: not-allowed;
        }

        /* Edit button */
        .edit-button {
            margin-bottom: 2px;
            background-color: transparent;
            color: #b148f3;
            border: 1px solid #b148f3;
            border-radius: 500px;
            padding: 8px 24px;
            font-size: 12px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1.76px;
            min-width: 80px;
        }

        .edit-button:hover {
            background-color: #b148f3;
            color: #ffffff;
            transform: scale(1.04);
        }

        /* Form actions */
        .form-actions {
            display: flex;
            justify-content: center;
            margin-top: 28px;
        }

        /* Save button */
        .save-button {
            background-color: #b148f3;
            color: #ffffff;
            border: none;
            border-radius: 500px;
            padding: 16px 48px;
            font-size: 14px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1.76px;
        }

        .save-button:hover {
            background-color: #9a3dd9;
            transform: scale(1.04);
        }

        .save-button:disabled {
            background-color: #333333;
            color: #b3b3b3;
            cursor: not-allowed;
            transform: none;
        }

        /* Flash messages */
        .flash-messages {
            position: fixed;
            top: 85px;
            right: 20px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .flash-messages li {
            background-color: rgba(177, 72, 243, 0.9);
            color: white;
            padding: 15px 25px;
            margin-top: 10px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            font-size: 14px;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 5s forwards;
            cursor: pointer;
            min-width: 250px;
            border: 1px solid #b148f3;
        }

        @keyframes slideInOut {
            0% { opacity: 0; transform: translateX(100%); }
            10% { opacity: 1; transform: translateX(0); }
            90% { opacity: 1; transform: translateX(0); }
            100% { opacity: 0; transform: translateX(100%); }
        }

        /* Modal styles */
        .modal-overlay {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            margin: 10% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(177, 72, 243, 0.3);
            position: relative;
        }

        .modal-content h2 {
            color: #b148f3;
            margin-bottom: 25px;
            text-align: center;
            font-size: 24px;
            font-weight: 600;
        }

        .modal-form-group {
            margin-bottom: 20px;
        }

        .modal-form-group label {
            display: block;
            margin-bottom: 8px;
            color: #ffffff;
            font-weight: 500;
            font-size: 14px;
        }

        .modal-form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #444444;
            border-radius: 8px;
            background-color: #333333;
            color: #ffffff;
            font-size: 16px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .modal-form-group input:focus {
            outline: none;
            border-color: #b148f3;
            box-shadow: 0 0 0 3px rgba(177, 72, 243, 0.1);
        }

        .modal-tooltip {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .modal-tooltip .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: rgba(0, 0, 0, 0.9);
            color: #fff;
            text-align: left;
            border-radius: 8px;
            padding: 15px;
            position: absolute;
            z-index: 1001;
            bottom: 125%;
            left: 0;
            opacity: 0;
            transition: opacity 0.3s;
            border: 1px solid #b148f3;
            font-size: 12px;
            line-height: 1.4;
        }

        .modal-tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .tooltip-criteria {
            list-style: none;
            padding: 0;
            margin: 10px 0 0 0;
        }

        .tooltip-criteria li {
            padding: 3px 0;
            color: #ff6b6b;
            font-size: 11px;
        }

        .tooltip-criteria li::before {
            content: "✗ ";
            margin-right: 5px;
        }

        .tooltip-criteria li.valid {
            color: #51cf66;
        }

        .tooltip-criteria li.valid::before {
            content: "✓ ";
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
        }

        .modal-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 1px;
            min-width: 120px;
        }

        .btn-update {
            background: linear-gradient(135deg, #b148f3 0%, #9a3dd9 100%);
            color: #ffffff;
        }

        .btn-update:hover:enabled {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(177, 72, 243, 0.4);
        }

        .btn-update:disabled {
            background-color: #333333;
            color: #666666;
            cursor: not-allowed;
            transform: none;
        }

        .btn-cancel {
            background-color: transparent;
            color: #b3b3b3;
            border: 1px solid #666666;
        }

        .btn-cancel:hover {
            background-color: #666666;
            color: #ffffff;
            transform: translateY(-2px);
        }

        /* Modal Flash Messages */
        .modal-flash-messages {
            margin-bottom: 20px;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            line-height: 1.4;
        }

        .modal-flash-messages.error {
            background-color: rgba(233, 20, 41, 0.2);
            border: 1px solid #ff0019a8;
            color: #ffffff;
        }

        .modal-flash-messages.success {
            background-color: rgba(29, 185, 84, 0.2);
            border: 1px solid #1DB954;
            color: #ffffff;
        }

        .modal-flash-content {
            margin: 0;
        }

        /* Profile container */
        .profile-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            padding: 20px;
        }

        .image-container {
            margin-bottom: 24px;
            position: relative;
        }

        .profile-picture {
            border-radius: 50%;
            width: 160px;
            height: 160px;
            object-fit: cover;
            border: 4px solid #b148f3;
            box-shadow: 0 8px 24px rgba(177, 72, 243, 0.3);
        }

        /* File input styling */
        #profile_picture {
            background-color: transparent;
            color: #b3b3b3;
            border: 1px solid #444444;
            border-radius: 25px;
            padding: 8px 20px;
            margin-top: 10px;
            font-size: 12px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        #profile_picture::-webkit-file-upload-button {
            background: linear-gradient(135deg, #b148f3 0%, #9a3dd9 100%);
            color: #ffffff;
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            margin-right: 12px;
            font-weight: 700;
            cursor: pointer;
            text-transform: uppercase;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        #profile_picture:hover {
            border-color: #b148f3;
            transform: scale(1.02);
        }

        #profile_picture::-webkit-file-upload-button:hover {
            background: linear-gradient(135deg, #9a3dd9 0%, #8a2be2 100%);
        }



        /* Responsive design */
        @media (max-width: 768px) {
            .content {
                padding: 24px 16px;
            }

            .page-title {
                font-size: 32px;
                margin-bottom: 32px;
            }

            .profile-picture {
                width: 150px;
                height: 150px;
            }

            .form-section {
                padding: 24px 16px;
            }

            .form-group.row {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
                padding: 16px 0;
            }

            .form-label {
                min-width: auto;
                margin-bottom: 8px;
            }

            .form-input {
                width: 100%;
            }

            .edit-button {
                align-self: flex-start;
                margin-top: 8px;
            }

            .flash-messages {
                right: 16px;
                left: 16px;
            }

            .flash-messages li {
                min-width: auto;
            }
        }

        @media (max-width: 480px) {
            .content {
                padding: 16px 12px;
            }

            .page-title {
                font-size: 28px;
            }

            .profile-container {
                padding: 24px 16px;
            }

            .profile-picture {
                width: 120px;
                height: 120px;
            }

            .form-section {
                padding: 20px 12px;
            }

            .save-button {
                padding: 14px 32px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="content">
        <h1 class="page-title">Admin Profile</h1>

        <form action="{{ url_for('admin_profile') }}" method="POST" enctype="multipart/form-data">
            <!-- Profile Picture Section -->
            <div class="profile-container">
                <div class="image-container">
                    {% if profile_picture %}
                        <img src="data:image/jpeg;base64,{{ profile_picture | b64encode }}" class="profile-picture" alt="Profile Picture">
                    {% else %}
                        <img src="{{ url_for('static', filename='default_profile.png') }}" class="profile-picture" alt="Default Profile Picture">
                    {% endif %}
                </div>
                <input type="file" id="profile_picture" name="profile_picture" accept="image/*">
            </div>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="flash-messages" id="flash-messages">
                        {% for category, message in messages %}
                            <li class="{{ category }}">{{ message }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}

            <!-- Admin Information Section -->
            <div class="form-section">
                <h2 style="margin-bottom: 0px; margin-top: 10px;">Admin Information</h2>

                <div class="form-group row">
                    <label for="username" class="form-label">Username</label>
                    <div class="form-info">
                        <span id="username_display">{{ username }}</span>
                        <input type="text" id="username_input" name="username" value="{{ username }}" class="form-input" style="display:none;" required>
                    </div>
                    <button type="button" class="edit-button" onclick="enableEdit('username')">Edit</button>
                </div>

                <div class="form-group row">
                    <label for="email" class="form-label">Email</label>
                    <div class="form-info">
                        <span id="email_display">{{ email or 'Not set' }}</span>
                        <input type="email" id="email_input" name="email" value="{{ email or '' }}" class="form-input" style="display:none;">
                    </div>
                    <button type="button" class="edit-button" onclick="enableEdit('email')">Edit</button>
                </div>

                <div class="form-group row">
                    <label for="password" class="form-label">Password</label>
                    <div class="form-info">••••••••••</div>
                    <button type="button" class="edit-button" onclick="enableEdit('password')">Edit</button>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="save-button">Save Changes</button>
            </div>
        </form>
    </div>

    <!-- Password Change Modal -->
    <div class="modal-overlay" id="passwordModal">
        <div class="modal-content">
            <h2>Change Password</h2>

            <!-- Modal Flash Messages -->
            <div id="modal-flash-messages" class="modal-flash-messages" style="display: none;">
                <div id="modal-flash-content" class="modal-flash-content"></div>
            </div>

            <form id="passwordForm" method="POST" action="{{ url_for('admin_profile') }}">
                <input type="hidden" name="action" value="change_password">

                <div class="modal-form-group">
                    <label for="current_password">Current Password:</label>
                    <input type="password" id="current_password" name="current_password" required>
                </div>

                <div class="modal-form-group">
                    <label for="new_password">New Password:</label>
                    <div class="modal-tooltip">
                        <input type="password" id="new_password" name="new_password" oninput="validateModalPassword()" required>
                        <span class="tooltiptext">
                            <strong>Password Requirements:</strong>
                            <ul class="tooltip-criteria">
                                <li id="modal-uppercase">At least 1 uppercase letter (A-Z)</li>
                                <li id="modal-lowercase">At least 1 lowercase letter (a-z)</li>
                                <li id="modal-digit">At least 1 digit (0-9)</li>
                                <li id="modal-symbol">At least 1 symbol (!@#$%^&*)</li>
                                <li id="modal-length">At least 8 characters</li>
                            </ul>
                        </span>
                    </div>
                </div>

                <div class="modal-buttons">
                    <button type="button" class="modal-btn btn-cancel" onclick="closePasswordModal()">Cancel</button>
                    <button type="submit" class="modal-btn btn-update" id="updatePasswordBtn" disabled>Update Password</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function enableEdit(fieldName) {
            if (fieldName === 'password') {
                // Show password modal instead of inline editing
                showPasswordModal();
                return;
            }

            const display = document.getElementById(fieldName + "_display");
            const input = document.getElementById(fieldName + "_input");
            const button = event.target;

            if (display && input) {
                display.style.display = "none";
                input.style.display = "block";
                input.focus();

                // Change button text to "Cancel" and add cancel functionality
                button.textContent = "Cancel";
                button.onclick = function() {
                    cancelEdit(fieldName);
                };
            }
        }

        function cancelEdit(fieldName) {
            const display = document.getElementById(fieldName + "_display");
            const input = document.getElementById(fieldName + "_input");
            const button = event.target;

            if (display && input) {
                display.style.display = "block";
                input.style.display = "none";

                // Reset input value to original
                if (fieldName === 'username') {
                    input.value = "{{ username }}";
                } else if (fieldName === 'email') {
                    input.value = "{{ email or '' }}";
                }

                // Change button back to "Edit"
                button.textContent = "Edit";
                button.onclick = function() {
                    enableEdit(fieldName);
                };
            }
        }

        // Password Modal Functions
        function showPasswordModal() {
            document.getElementById('passwordModal').style.display = 'flex';
            document.getElementById('current_password').focus();
        }

        function closePasswordModal() {
            document.getElementById('passwordModal').style.display = 'none';
            // Reset form
            document.getElementById('passwordForm').reset();
            // Reset validation
            resetPasswordValidation();
            // Hide flash messages
            hideModalFlash();
        }

        function showModalFlash(message, type) {
            const flashContainer = document.getElementById('modal-flash-messages');
            const flashContent = document.getElementById('modal-flash-content');

            flashContent.textContent = message;
            flashContainer.className = 'modal-flash-messages ' + type;
            flashContainer.style.display = 'block';

            // Auto-hide after 5 seconds
            setTimeout(hideModalFlash, 5000);
        }

        function hideModalFlash() {
            const flashContainer = document.getElementById('modal-flash-messages');
            flashContainer.style.display = 'none';
            flashContainer.className = 'modal-flash-messages';
        }

        function validateModalPassword() {
            const password = document.getElementById("new_password").value;
            const updateBtn = document.getElementById("updatePasswordBtn");

            // Regular Expressions for Password
            const hasUpper = /[A-Z]/.test(password);
            const hasLower = /[a-z]/.test(password);
            const hasDigit = /[0-9]/.test(password);
            const hasSymbol = /[!@#$%^&*]/.test(password);
            const isLongEnough = password.length >= 8;

            // Toggle valid/invalid classes for Password tooltip
            document.getElementById("modal-uppercase").classList.toggle("valid", hasUpper);
            document.getElementById("modal-lowercase").classList.toggle("valid", hasLower);
            document.getElementById("modal-digit").classList.toggle("valid", hasDigit);
            document.getElementById("modal-symbol").classList.toggle("valid", hasSymbol);
            document.getElementById("modal-length").classList.toggle("valid", isLongEnough);

            // Enable/disable update button based on validation
            const allValid = hasUpper && hasLower && hasDigit && hasSymbol && isLongEnough;
            updateBtn.disabled = !allValid;
        }

        function resetPasswordValidation() {
            const criteria = ['modal-uppercase', 'modal-lowercase', 'modal-digit', 'modal-symbol', 'modal-length'];
            criteria.forEach(id => {
                document.getElementById(id).classList.remove('valid');
            });
            document.getElementById("updatePasswordBtn").disabled = true;
        }

        // Auto-hide flash messages after animation
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.getElementById('flash-messages');
            if (flashMessages) {
                setTimeout(function() {
                    flashMessages.style.display = 'none';
                }, 5000);
            }

            // Close modal when clicking outside of it
            const modal = document.getElementById('passwordModal');
            modal.addEventListener('click', function(event) {
                if (event.target === modal) {
                    closePasswordModal();
                }
            });

            // Prevent closing when clicking inside modal content
            const modalContent = modal.querySelector('.modal-content');
            modalContent.addEventListener('click', function(event) {
                event.stopPropagation();
            });

            // Handle password form submission with AJAX
            const passwordForm = document.getElementById('passwordForm');
            passwordForm.addEventListener('submit', function(event) {
                event.preventDefault();

                const formData = new FormData(passwordForm);
                const updateBtn = document.getElementById('updatePasswordBtn');

                // Disable button and show loading state
                updateBtn.disabled = true;
                updateBtn.textContent = 'Updating...';

                fetch('{{ url_for("admin_profile") }}', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .catch(() => {
                    // If JSON parsing fails, treat as error
                    return { success: false, message: 'An error occurred while updating password.' };
                })
                .then(data => {
                    if (data.success) {
                        showModalFlash(data.message, 'success');
                        // Close modal after 2 seconds on success
                        setTimeout(() => {
                            closePasswordModal();
                            // Reload page to refresh any session data
                            window.location.reload();
                        }, 2000);
                    } else {
                        showModalFlash(data.message, 'error');
                    }
                })
                .finally(() => {
                    // Re-enable button and reset text
                    updateBtn.disabled = false;
                    updateBtn.textContent = 'Update Password';
                    // Re-validate to set correct disabled state
                    validateModalPassword();
                });
            });
        });
    </script>
</body>
</html>

{% endblock %}

