# Troubleshooting Guide

This guide helps you diagnose and fix common issues with the URL Scanner Docker deployment.

## 🚨 Quick Diagnostics

### Check Service Status
```bash
# Check if all containers are running
docker-compose ps

# Check container health
./monitor.sh status

# Check logs for errors
./manage.sh logs
```

### Common Commands
```bash
# Restart all services
./manage.sh restart

# Check resource usage
./monitor.sh resources

# Test application health
curl http://localhost:5000/health
```

## 🐛 Common Issues and Solutions

### 1. Containers Won't Start

#### Symptoms
- `docker-compose up` fails
- Containers exit immediately
- "Port already in use" errors

#### Solutions

**Port Conflicts**
```bash
# Check what's using the port
netstat -tulpn | grep :5000
# or on Windows
netstat -an | findstr :5000

# Kill the process or change ports in docker-compose.yml
```

**Permission Issues (Linux/macOS)**
```bash
# Fix script permissions
chmod +x *.sh

# Fix file ownership
sudo chown -R $USER:$USER .

# Fix Docker socket permissions
sudo usermod -aG docker $USER
# Then logout and login again
```

**Insufficient Resources**
```bash
# Check available resources
docker system df
free -h  # Linux
# Increase Docker memory limit in Docker Desktop settings
```

### 2. Database Connection Issues

#### Symptoms
- "Can't connect to MySQL server" errors
- Application shows database errors
- Health check fails

#### Solutions

**Database Not Ready**
```bash
# Wait for database to initialize (first run takes longer)
./monitor.sh database

# Check database logs
./manage.sh logs-db

# Restart database service
docker-compose restart mysql
```

**Wrong Credentials**
```bash
# Verify environment variables
cat .env

# Reset database with correct credentials
docker-compose down -v
docker-compose up -d
```

**Database Corruption**
```bash
# Backup current data
./manage.sh backup

# Reset database
docker-compose down -v
docker volume rm urlscanner_mysql_data
docker-compose up -d
```

### 3. Web Application Issues

#### Symptoms
- 500 Internal Server Error
- Application won't load
- Features not working

#### Solutions

**Check Application Logs**
```bash
# View recent logs
./manage.sh logs-web

# Follow logs in real-time
docker-compose logs -f web
```

**Missing Dependencies**
```bash
# Rebuild with no cache
docker-compose build --no-cache web
docker-compose up -d
```

**Configuration Issues**
```bash
# Verify environment variables
docker-compose exec web env | grep -E "(DB_|FLASK_)"

# Check file permissions in container
docker-compose exec web ls -la /app
```

### 4. Email/OTP Issues

#### Symptoms
- OTP emails not sent
- Email verification fails
- SMTP errors in logs

#### Solutions

**Gmail Configuration**
```bash
# Verify Gmail settings in .env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password  # Not your regular password!
EMAIL_USE_TLS=True
```

**Test Email Configuration**
```bash
# Access web container
./manage.sh shell-web

# Test SMTP connection
python3 -c "
import smtplib
smtp = smtplib.SMTP('smtp.gmail.com', 587)
smtp.starttls()
smtp.login('<EMAIL>', 'your_app_password')
print('SMTP connection successful')
smtp.quit()
"
```

### 5. Performance Issues

#### Symptoms
- Slow response times
- High CPU/memory usage
- Timeouts

#### Solutions

**Check Resource Usage**
```bash
# Monitor resources
./monitor.sh resources

# Check for memory leaks
docker stats --no-stream
```

**Optimize Configuration**
```bash
# Reduce worker count in Dockerfile
# Change from --workers 4 to --workers 2

# Increase timeout
# Change --timeout 120 to --timeout 300
```

**Database Optimization**
```bash
# Check slow queries
./manage.sh shell-db
SHOW PROCESSLIST;
SHOW FULL PROCESSLIST;

# Add indexes for better performance
CREATE INDEX idx_scan_results_timestamp ON scan_results(timestamp);
```

### 6. SSL/HTTPS Issues

#### Symptoms
- Certificate errors
- Mixed content warnings
- HTTPS not working

#### Solutions

**Development Environment**
```bash
# Use HTTP for development
# Update .env
SESSION_COOKIE_SECURE=False
```

**Production with Reverse Proxy**
```nginx
# Nginx configuration
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 7. File Upload Issues

#### Symptoms
- Profile picture uploads fail
- File size errors
- Permission denied errors

#### Solutions

**Check Upload Directory**
```bash
# Verify upload directory exists and has correct permissions
docker-compose exec web ls -la /app/static/uploads

# Create directory if missing
docker-compose exec web mkdir -p /app/static/uploads
docker-compose exec web chown appuser:appuser /app/static/uploads
```

**File Size Limits**
```bash
# Check current limit in .env
MAX_CONTENT_LENGTH=524288000  # 500MB

# Adjust as needed and restart
./manage.sh restart
```

## 🔧 Advanced Troubleshooting

### Container Debugging

**Access Container Shell**
```bash
# Web container
./manage.sh shell-web

# Database container
docker-compose exec mysql /bin/bash
```

**Check Container Logs**
```bash
# All containers
docker-compose logs

# Specific container with timestamps
docker-compose logs -t web

# Follow logs
docker-compose logs -f --tail=100
```

**Inspect Container Configuration**
```bash
# Detailed container info
docker inspect urlscanner_web

# Check environment variables
docker-compose exec web env
```

### Network Issues

**Check Container Networking**
```bash
# List networks
docker network ls

# Inspect network
docker network inspect urlscanner_urlscanner_network

# Test connectivity between containers
docker-compose exec web ping mysql
```

**Port Mapping Issues**
```bash
# Check port mappings
docker-compose ps

# Test port accessibility
telnet localhost 5000
curl -v http://localhost:5000/health
```

### Database Debugging

**Check Database Status**
```bash
# Connect to database
./manage.sh shell-db

# Check database size
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'urlscanner'
GROUP BY table_schema;

# Check table status
SHOW TABLE STATUS FROM urlscanner;
```

**Performance Analysis**
```bash
# Check slow queries
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

# Check process list
SHOW PROCESSLIST;

# Check index usage
EXPLAIN SELECT * FROM scan_results WHERE sender = 'test';
```

## 📊 Monitoring and Alerts

### Set Up Monitoring

**Basic Health Monitoring**
```bash
# Create monitoring cron job
crontab -e

# Add this line to check every 5 minutes
*/5 * * * * /path/to/your/project/monitor.sh health >> /var/log/urlscanner-health.log 2>&1
```

**Log Monitoring**
```bash
# Monitor for errors
tail -f logs/app.log | grep ERROR

# Set up log rotation
sudo logrotate -d /etc/logrotate.d/urlscanner
```

### Performance Monitoring

**Resource Usage Alerts**
```bash
# Monitor CPU usage
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Set up alerts for high resource usage
# (Implementation depends on your monitoring system)
```

## 🆘 Emergency Procedures

### Service Recovery

**Complete Service Restart**
```bash
# Stop all services
docker-compose down

# Clean up
docker system prune -f

# Restart services
docker-compose up -d

# Verify health
./monitor.sh status
```

**Database Recovery**
```bash
# If database is corrupted
docker-compose down
docker volume rm urlscanner_mysql_data

# Restore from backup
./manage.sh restore backups/latest_backup.sql
```

### Rollback Procedures

**Application Rollback**
```bash
# Stop current version
docker-compose down

# Checkout previous version
git checkout previous-stable-tag

# Rebuild and start
docker-compose up --build -d
```

**Database Rollback**
```bash
# Restore from backup
./manage.sh restore backups/pre_deployment_backup.sql
```

## 📞 Getting Help

### Information to Collect

When reporting issues, please provide:

1. **System Information**
   ```bash
   # Docker version
   docker --version
   docker-compose --version
   
   # System info
   uname -a
   free -h
   df -h
   ```

2. **Service Status**
   ```bash
   # Container status
   docker-compose ps
   
   # Health check
   ./monitor.sh status
   ```

3. **Logs**
   ```bash
   # Recent logs
   ./manage.sh logs > logs_$(date +%Y%m%d_%H%M%S).txt
   ```

4. **Configuration**
   ```bash
   # Environment (remove sensitive data)
   cat .env | sed 's/PASSWORD=.*/PASSWORD=***REDACTED***/'
   ```

### Support Channels

- Check the GitHub issues for similar problems
- Review the documentation thoroughly
- Use the monitoring tools to gather diagnostic information
- Create detailed issue reports with logs and system information

---

**Remember**: Most issues can be resolved by checking logs, verifying configuration, and ensuring all services are healthy. Always backup your data before making significant changes.
